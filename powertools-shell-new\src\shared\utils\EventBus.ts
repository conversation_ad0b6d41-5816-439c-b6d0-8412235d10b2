/**
 * 事件总线
 * 重构自原有的 eventbus.ts，提供类型安全的事件发布订阅功能
 */

import { EventEmitter } from 'events'

/**
 * 事件监听器类型
 */
export type EventListener<T = any> = (data: T) => void

/**
 * 事件处理器映射类型
 */
export type EventHandlerMap = Record<string, EventListener[]>

/**
 * 事件总线类
 * 提供发布订阅模式的事件通信机制
 */
export class EventBus {
  private topics: EventHandlerMap
  private maxListeners: number

  constructor(maxListeners: number = 100) {
    this.topics = {}
    this.maxListeners = maxListeners
  }

  /**
   * 订阅事件
   * @param topic 事件主题
   * @param handler 事件处理器
   */
  subscribe<T = any>(topic: string, handler: EventListener<T>): void {
    if (!topic || typeof handler !== 'function') {
      throw new Error('Invalid topic or handler')
    }

    let topicHandlers = this.topics[topic]
    if (!topicHandlers) {
      topicHandlers = []
      this.topics[topic] = topicHandlers
    }

    // 检查监听器数量限制
    if (topicHandlers.length >= this.maxListeners) {
      console.warn(`EventBus: Maximum listeners (${this.maxListeners}) exceeded for topic '${topic}'`)
    }

    topicHandlers.push(handler)
  }

  /**
   * 取消订阅事件
   * @param topic 事件主题
   * @param handler 事件处理器
   */
  unsubscribe<T = any>(topic: string, handler: EventListener<T>): void {
    const topicHandlers = this.topics[topic]
    if (!topicHandlers) {
      return
    }

    const index = topicHandlers.indexOf(handler)
    if (index !== -1) {
      topicHandlers.splice(index, 1)
    }

    // 如果没有监听器了，删除主题
    if (topicHandlers.length === 0) {
      delete this.topics[topic]
    }
  }

  /**
   * 发布事件
   * @param topic 事件主题
   * @param data 事件数据
   */
  publish<T = any>(topic: string, data?: T): void {
    const topicHandlers = this.topics[topic]
    if (!topicHandlers || topicHandlers.length === 0) {
      console.debug(`EventBus: No listeners for topic '${topic}'`)
      return
    }

    // 异步执行所有处理器，避免阻塞
    setImmediate(() => {
      topicHandlers.forEach(handler => {
        try {
          handler(data)
        } catch (error) {
          console.error(`EventBus: Error in handler for topic '${topic}':`, error)
        }
      })
    })
  }

  /**
   * 同步发布事件
   * @param topic 事件主题
   * @param data 事件数据
   */
  publishSync<T = any>(topic: string, data?: T): void {
    const topicHandlers = this.topics[topic]
    if (!topicHandlers || topicHandlers.length === 0) {
      console.debug(`EventBus: No listeners for topic '${topic}'`)
      return
    }

    topicHandlers.forEach(handler => {
      try {
        handler(data)
      } catch (error) {
        console.error(`EventBus: Error in handler for topic '${topic}':`, error)
      }
    })
  }

  /**
   * 一次性订阅事件
   * @param topic 事件主题
   * @param handler 事件处理器
   */
  once<T = any>(topic: string, handler: EventListener<T>): void {
    const onceHandler = (data: T) => {
      handler(data)
      this.unsubscribe(topic, onceHandler)
    }
    this.subscribe(topic, onceHandler)
  }

  /**
   * 移除主题的所有监听器
   * @param topic 事件主题
   */
  removeAllListeners(topic?: string): void {
    if (topic) {
      delete this.topics[topic]
    } else {
      this.topics = {}
    }
  }

  /**
   * 获取主题的监听器数量
   * @param topic 事件主题
   */
  listenerCount(topic: string): number {
    const topicHandlers = this.topics[topic]
    return topicHandlers ? topicHandlers.length : 0
  }

  /**
   * 获取所有主题列表
   */
  getTopics(): string[] {
    return Object.keys(this.topics)
  }

  /**
   * 检查是否有指定主题的监听器
   * @param topic 事件主题
   */
  hasListeners(topic: string): boolean {
    return this.listenerCount(topic) > 0
  }

  /**
   * 设置最大监听器数量
   * @param max 最大数量
   */
  setMaxListeners(max: number): void {
    this.maxListeners = max
  }

  /**
   * 获取最大监听器数量
   */
  getMaxListeners(): number {
    return this.maxListeners
  }

  /**
   * 清理所有事件监听器
   */
  dispose(): void {
    this.topics = {}
  }
}

/**
 * 类型化事件总线
 * 提供类型安全的事件定义
 */
export class TypedEventBus<TEventMap extends Record<string, any>> {
  private eventBus: EventBus

  constructor(maxListeners?: number) {
    this.eventBus = new EventBus(maxListeners)
  }

  /**
   * 订阅事件
   */
  subscribe<K extends keyof TEventMap>(
    topic: K,
    handler: EventListener<TEventMap[K]>
  ): void {
    this.eventBus.subscribe(topic as string, handler)
  }

  /**
   * 取消订阅事件
   */
  unsubscribe<K extends keyof TEventMap>(
    topic: K,
    handler: EventListener<TEventMap[K]>
  ): void {
    this.eventBus.unsubscribe(topic as string, handler)
  }

  /**
   * 发布事件
   */
  publish<K extends keyof TEventMap>(topic: K, data: TEventMap[K]): void {
    this.eventBus.publish(topic as string, data)
  }

  /**
   * 同步发布事件
   */
  publishSync<K extends keyof TEventMap>(topic: K, data: TEventMap[K]): void {
    this.eventBus.publishSync(topic as string, data)
  }

  /**
   * 一次性订阅事件
   */
  once<K extends keyof TEventMap>(
    topic: K,
    handler: EventListener<TEventMap[K]>
  ): void {
    this.eventBus.once(topic as string, handler)
  }

  /**
   * 移除所有监听器
   */
  removeAllListeners<K extends keyof TEventMap>(topic?: K): void {
    this.eventBus.removeAllListeners(topic as string)
  }

  /**
   * 获取监听器数量
   */
  listenerCount<K extends keyof TEventMap>(topic: K): number {
    return this.eventBus.listenerCount(topic as string)
  }

  /**
   * 检查是否有监听器
   */
  hasListeners<K extends keyof TEventMap>(topic: K): boolean {
    return this.eventBus.hasListeners(topic as string)
  }

  /**
   * 清理
   */
  dispose(): void {
    this.eventBus.dispose()
  }
}

/**
 * 全局事件总线实例
 */
let globalEventBus: EventBus | null = null

/**
 * 初始化全局事件总线
 */
export function initializeGlobalEventBus(maxListeners?: number): void {
  globalEventBus = new EventBus(maxListeners)
}

/**
 * 获取全局事件总线
 */
export function getGlobalEventBus(): EventBus {
  if (!globalEventBus) {
    initializeGlobalEventBus()
  }
  return globalEventBus!
}

/**
 * 销毁全局事件总线
 */
export function destroyGlobalEventBus(): void {
  if (globalEventBus) {
    globalEventBus.dispose()
    globalEventBus = null
  }
}

// 导出默认实例
export default {
  EventBus,
  TypedEventBus,
  initializeGlobalEventBus,
  getGlobalEventBus,
  destroyGlobalEventBus
}
