/**
 * 日志工具
 * 提供类型安全的日志记录功能
 */

import { EventEmitter } from 'events'
import type { LogLevel, LogEntry, Logger as ILogger } from '../types'

/**
 * 日志配置接口
 */
export interface LoggerConfig {
  level: LogLevel
  enableConsole: boolean
  enableFile: boolean
  maxFileSize: number
  maxFiles: number
  datePattern: string
  format: 'json' | 'text'
  category: string
}

/**
 * 日志格式化器接口
 */
export interface LogFormatter {
  format(entry: LogEntry): string
}

/**
 * 日志传输器接口
 */
export interface LogTransport {
  log(entry: LogEntry): void
  close(): void
}

/**
 * 控制台传输器
 */
export class ConsoleTransport implements LogTransport {
  private colors: Record<LogLevel, string> = {
    debug: '\x1b[36m',    // cyan
    info: '\x1b[32m',     // green
    warn: '\x1b[33m',     // yellow
    error: '\x1b[31m',    // red
    fatal: '\x1b[35m'     // magenta
  }

  private reset = '\x1b[0m'

  log(entry: LogEntry): void {
    const color = this.colors[entry.level] || ''
    const timestamp = entry.timestamp.toISOString()
    const level = entry.level.toUpperCase().padEnd(5)
    const category = entry.category ? `[${entry.category}]` : ''
    
    const message = `${color}${timestamp} ${level}${this.reset} ${category} ${entry.message}`
    
    switch (entry.level) {
      case 'error':
      case 'fatal':
        console.error(message, entry.data || '', entry.stack || '')
        break
      case 'warn':
        console.warn(message, entry.data || '')
        break
      case 'debug':
        console.debug(message, entry.data || '')
        break
      default:
        console.log(message, entry.data || '')
    }
  }

  close(): void {
    // Console transport doesn't need cleanup
  }
}

/**
 * 文件传输器（简化版，实际项目中可能需要更复杂的实现）
 */
export class FileTransport implements LogTransport {
  private entries: LogEntry[] = []
  private maxEntries: number

  constructor(private filePath: string, maxEntries: number = 1000) {
    this.maxEntries = maxEntries
  }

  log(entry: LogEntry): void {
    this.entries.push(entry)
    
    // 简单的内存限制
    if (this.entries.length > this.maxEntries) {
      this.entries = this.entries.slice(-this.maxEntries)
    }
  }

  getEntries(): LogEntry[] {
    return [...this.entries]
  }

  clear(): void {
    this.entries = []
  }

  close(): void {
    this.entries = []
  }
}

/**
 * JSON格式化器
 */
export class JsonFormatter implements LogFormatter {
  format(entry: LogEntry): string {
    return JSON.stringify(entry)
  }
}

/**
 * 文本格式化器
 */
export class TextFormatter implements LogFormatter {
  format(entry: LogEntry): string {
    const timestamp = entry.timestamp.toISOString()
    const level = entry.level.toUpperCase().padEnd(5)
    const category = entry.category ? `[${entry.category}]` : ''
    const data = entry.data ? ` ${JSON.stringify(entry.data)}` : ''
    const stack = entry.stack ? `\n${entry.stack}` : ''
    
    return `${timestamp} ${level} ${category} ${entry.message}${data}${stack}`
  }
}

/**
 * 日志器实现
 */
export class Logger extends EventEmitter implements ILogger {
  private config: LoggerConfig
  private transports: LogTransport[] = []
  private formatter: LogFormatter

  constructor(config: Partial<LoggerConfig> = {}) {
    super()
    
    this.config = {
      level: 'info',
      enableConsole: true,
      enableFile: false,
      maxFileSize: 10 * 1024 * 1024, // 10MB
      maxFiles: 5,
      datePattern: 'YYYY-MM-DD',
      format: 'text',
      category: '',
      ...config
    }

    this.formatter = this.config.format === 'json' 
      ? new JsonFormatter() 
      : new TextFormatter()

    this.setupTransports()
  }

  private setupTransports(): void {
    if (this.config.enableConsole) {
      this.transports.push(new ConsoleTransport())
    }

    if (this.config.enableFile) {
      const filePath = `logs/${this.config.category || 'app'}.log`
      this.transports.push(new FileTransport(filePath))
    }
  }

  private shouldLog(level: LogLevel): boolean {
    const levels: LogLevel[] = ['debug', 'info', 'warn', 'error', 'fatal']
    const currentLevelIndex = levels.indexOf(this.config.level)
    const messageLevelIndex = levels.indexOf(level)
    
    return messageLevelIndex >= currentLevelIndex
  }

  private log(level: LogLevel, message: string, data?: any, error?: Error): void {
    if (!this.shouldLog(level)) {
      return
    }

    const entry: LogEntry = {
      timestamp: new Date(),
      level,
      category: this.config.category,
      message,
      data,
      stack: error?.stack
    }

    this.transports.forEach(transport => {
      try {
        transport.log(entry)
      } catch (err) {
        console.error('Logger transport error:', err)
      }
    })

    this.emit('log', entry)
  }

  debug(message: string, data?: any): void {
    this.log('debug', message, data)
  }

  info(message: string, data?: any): void {
    this.log('info', message, data)
  }

  warn(message: string, data?: any): void {
    this.log('warn', message, data)
  }

  error(message: string, error?: Error, data?: any): void {
    this.log('error', message, data, error)
  }

  fatal(message: string, error?: Error, data?: any): void {
    this.log('fatal', message, data, error)
  }

  child(category: string): Logger {
    const childCategory = this.config.category 
      ? `${this.config.category}.${category}`
      : category

    return new Logger({
      ...this.config,
      category: childCategory
    })
  }

  setLevel(level: LogLevel): void {
    this.config.level = level
  }

  getLevel(): LogLevel {
    return this.config.level
  }

  addTransport(transport: LogTransport): void {
    this.transports.push(transport)
  }

  removeTransport(transport: LogTransport): void {
    const index = this.transports.indexOf(transport)
    if (index !== -1) {
      this.transports.splice(index, 1)
      transport.close()
    }
  }

  close(): void {
    this.transports.forEach(transport => transport.close())
    this.transports = []
    this.removeAllListeners()
  }
}

/**
 * 日志管理器
 */
export class LogManager {
  private static instance: LogManager
  private loggers = new Map<string, Logger>()
  private globalConfig: Partial<LoggerConfig> = {}

  private constructor() {}

  static getInstance(): LogManager {
    if (!LogManager.instance) {
      LogManager.instance = new LogManager()
    }
    return LogManager.instance
  }

  setGlobalConfig(config: Partial<LoggerConfig>): void {
    this.globalConfig = { ...this.globalConfig, ...config }
  }

  getLogger(category: string = 'default'): Logger {
    if (!this.loggers.has(category)) {
      const logger = new Logger({
        ...this.globalConfig,
        category
      })
      this.loggers.set(category, logger)
    }
    return this.loggers.get(category)!
  }

  createLogger(category: string, config?: Partial<LoggerConfig>): Logger {
    const logger = new Logger({
      ...this.globalConfig,
      ...config,
      category
    })
    this.loggers.set(category, logger)
    return logger
  }

  removeLogger(category: string): void {
    const logger = this.loggers.get(category)
    if (logger) {
      logger.close()
      this.loggers.delete(category)
    }
  }

  getAllLoggers(): Logger[] {
    return Array.from(this.loggers.values())
  }

  setGlobalLevel(level: LogLevel): void {
    this.globalConfig.level = level
    this.loggers.forEach(logger => logger.setLevel(level))
  }

  close(): void {
    this.loggers.forEach(logger => logger.close())
    this.loggers.clear()
  }
}

// 创建默认日志器
const defaultLogger = new Logger({ category: 'app' })

// 导出便捷函数
export const debug = defaultLogger.debug.bind(defaultLogger)
export const info = defaultLogger.info.bind(defaultLogger)
export const warn = defaultLogger.warn.bind(defaultLogger)
export const error = defaultLogger.error.bind(defaultLogger)
export const fatal = defaultLogger.fatal.bind(defaultLogger)

// 导出默认对象
export default {
  Logger,
  LogManager,
  ConsoleTransport,
  FileTransport,
  JsonFormatter,
  TextFormatter,
  debug,
  info,
  warn,
  error,
  fatal
}
