/**
 * 等待对象
 * 重构自原有的 waitObject.js，提供类型安全的异步等待功能
 */

/**
 * 等待对象类
 * 提供Promise的手动控制功能
 */
export class WaitObject<T = any> {
  private _resolve!: (value: T | PromiseLike<T>) => void
  private _reject!: (reason?: any) => void
  private _promise: Promise<T>
  private _settled: boolean = false
  private _result?: T
  private _error?: any

  constructor() {
    this._promise = new Promise<T>((resolve, reject) => {
      this._resolve = (value: T | PromiseLike<T>) => {
        if (!this._settled) {
          this._settled = true
          this._result = value as T
          resolve(value)
        }
      }
      
      this._reject = (reason?: any) => {
        if (!this._settled) {
          this._settled = true
          this._error = reason
          reject(reason)
        }
      }
    })
  }

  /**
   * 等待Promise完成
   * @returns Promise
   */
  wait(): Promise<T> {
    return this._promise
  }

  /**
   * 解决Promise
   * @param value 解决值
   */
  resolve(value: T): void {
    this._resolve(value)
  }

  /**
   * 拒绝Promise
   * @param reason 拒绝原因
   */
  reject(reason?: any): void {
    this._reject(reason)
  }

  /**
   * 检查是否已完成
   * @returns 是否已完成
   */
  isSettled(): boolean {
    return this._settled
  }

  /**
   * 检查是否已解决
   * @returns 是否已解决
   */
  isResolved(): boolean {
    return this._settled && this._error === undefined
  }

  /**
   * 检查是否已拒绝
   * @returns 是否已拒绝
   */
  isRejected(): boolean {
    return this._settled && this._error !== undefined
  }

  /**
   * 获取结果（如果已解决）
   * @returns 结果值
   */
  getResult(): T | undefined {
    return this._result
  }

  /**
   * 获取错误（如果已拒绝）
   * @returns 错误信息
   */
  getError(): any {
    return this._error
  }

  /**
   * 添加完成回调
   * @param onFulfilled 成功回调
   * @param onRejected 失败回调
   * @returns Promise
   */
  then<TResult1 = T, TResult2 = never>(
    onFulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | null,
    onRejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | null
  ): Promise<TResult1 | TResult2> {
    return this._promise.then(onFulfilled, onRejected)
  }

  /**
   * 添加错误回调
   * @param onRejected 错误回调
   * @returns Promise
   */
  catch<TResult = never>(
    onRejected?: ((reason: any) => TResult | PromiseLike<TResult>) | null
  ): Promise<T | TResult> {
    return this._promise.catch(onRejected)
  }

  /**
   * 添加最终回调
   * @param onFinally 最终回调
   * @returns Promise
   */
  finally(onFinally?: (() => void) | null): Promise<T> {
    return this._promise.finally(onFinally)
  }
}

/**
 * 超时等待对象
 * 带有超时功能的等待对象
 */
export class TimeoutWaitObject<T = any> extends WaitObject<T> {
  private timeoutId?: NodeJS.Timeout

  constructor(timeoutMs?: number) {
    super()
    
    if (timeoutMs && timeoutMs > 0) {
      this.timeoutId = setTimeout(() => {
        this.reject(new Error(`Operation timed out after ${timeoutMs}ms`))
      }, timeoutMs)
    }
  }

  /**
   * 解决Promise（重写以清除超时）
   * @param value 解决值
   */
  resolve(value: T): void {
    this.clearTimeout()
    super.resolve(value)
  }

  /**
   * 拒绝Promise（重写以清除超时）
   * @param reason 拒绝原因
   */
  reject(reason?: any): void {
    this.clearTimeout()
    super.reject(reason)
  }

  /**
   * 清除超时
   */
  private clearTimeout(): void {
    if (this.timeoutId) {
      clearTimeout(this.timeoutId)
      this.timeoutId = undefined
    }
  }
}

/**
 * 可取消的等待对象
 */
export class CancellableWaitObject<T = any> extends WaitObject<T> {
  private _cancelled: boolean = false

  /**
   * 取消操作
   * @param reason 取消原因
   */
  cancel(reason?: string): void {
    if (!this.isSettled()) {
      this._cancelled = true
      this.reject(new Error(reason || 'Operation was cancelled'))
    }
  }

  /**
   * 检查是否已取消
   * @returns 是否已取消
   */
  isCancelled(): boolean {
    return this._cancelled
  }
}

/**
 * 等待对象工厂
 */
export class WaitObjectFactory {
  /**
   * 创建基础等待对象
   */
  static create<T = any>(): WaitObject<T> {
    return new WaitObject<T>()
  }

  /**
   * 创建超时等待对象
   * @param timeoutMs 超时时间（毫秒）
   */
  static createWithTimeout<T = any>(timeoutMs: number): TimeoutWaitObject<T> {
    return new TimeoutWaitObject<T>(timeoutMs)
  }

  /**
   * 创建可取消等待对象
   */
  static createCancellable<T = any>(): CancellableWaitObject<T> {
    return new CancellableWaitObject<T>()
  }

  /**
   * 创建已解决的等待对象
   * @param value 解决值
   */
  static createResolved<T>(value: T): WaitObject<T> {
    const waitObject = new WaitObject<T>()
    waitObject.resolve(value)
    return waitObject
  }

  /**
   * 创建已拒绝的等待对象
   * @param reason 拒绝原因
   */
  static createRejected<T = any>(reason: any): WaitObject<T> {
    const waitObject = new WaitObject<T>()
    waitObject.reject(reason)
    return waitObject
  }

  /**
   * 从Promise创建等待对象
   * @param promise Promise对象
   */
  static fromPromise<T>(promise: Promise<T>): WaitObject<T> {
    const waitObject = new WaitObject<T>()
    
    promise
      .then(value => waitObject.resolve(value))
      .catch(error => waitObject.reject(error))
    
    return waitObject
  }

  /**
   * 等待所有等待对象完成
   * @param waitObjects 等待对象数组
   */
  static async waitAll<T>(waitObjects: WaitObject<T>[]): Promise<T[]> {
    const promises = waitObjects.map(wo => wo.wait())
    return Promise.all(promises)
  }

  /**
   * 等待任一等待对象完成
   * @param waitObjects 等待对象数组
   */
  static async waitAny<T>(waitObjects: WaitObject<T>[]): Promise<T> {
    const promises = waitObjects.map(wo => wo.wait())
    return Promise.race(promises)
  }
}

// 导出默认对象
export default {
  WaitObject,
  TimeoutWaitObject,
  CancellableWaitObject,
  WaitObjectFactory
}
