<template>
  <div class="nx-layout-wrapper">
    <div v-show="configPanel" class="nx-layout-left">
      <!-- 菜单面板 -->
      <div class="nx-menu-panel">
        <h3>Sessions</h3>
        <p>Session management will be implemented here</p>
      </div>
    </div>
    
    <div class="nx-layout-right" :style="{ width: `calc(100% - ${configPanel ? 295 : 0}px)` }">
      <div
        class="nx-layout-toggle-bar"
        :class="{ 'nx-layout-toggle-bar--collapsed': configPanel }"
        @click="handleCollapsed"
      >
        <div class="nx-layout-toggle-bar__top"></div>
        <div class="nx-layout-toggle-bar__bottom"></div>
      </div>
      
      <!-- 标签页 -->
      <div v-if="showTabs" class="nx-tab-menu">
        <div class="tab-item active">
          <span>Welcome</span>
          <button class="tab-close">×</button>
        </div>
      </div>
      
      <div class="nx-content" :style="{ height: `calc(100% - ${showTabs ? '40px' : '0px'})` }">
        <keep-alive :exclude="['GlobalSetting', 'lock']">
          <router-view />
        </keep-alive>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onBeforeMount } from 'vue'
import { storeToRefs } from 'pinia'
import { useNxTabsStore } from '@/stores'

const nxTabsStore = useNxTabsStore()
const { configPanel, showTabs } = storeToRefs(nxTabsStore)

const handleCollapsed = () => {
  configPanel.value = !configPanel.value
  
  // 发布事件（后续实现事件总线）
  console.log(`Session config panel ${configPanel.value ? 'opened' : 'closed'}`)
}

onBeforeMount(async () => {
  try {
    // 这里将初始化会话管理器
    // 避免重复创建欢迎会话实例
    console.log('MainLayout mounted, initializing session manager...')
    
    // TODO: 实现会话管理器初始化
    // const sessionManager = getCurrentInstance()?.appContext.config.globalProperties.$sessionManager
    // if (!sessionManager.getSessionInstances().find((x) => x.name === 'Welcome')) {
    //   await sessionManager.createWelcomeSessionInstance()
    // }
    
  } catch (error) {
    console.error('Failed to initialize MainLayout:', error)
  }
})
</script>

<style scoped lang="scss">
.nx-layout-wrapper {
  display: flex;
  height: 100%;
  width: 100%;
}

.nx-layout-left {
  width: 295px;
  background: var(--nx-sidebar-bg, #f8f9fa);
  border-right: 1px solid var(--nx-border-color, #e0e0e0);
  overflow: hidden;
}

.nx-menu-panel {
  padding: 16px;
  height: 100%;
  
  h3 {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--nx-text-color, #333333);
  }
  
  p {
    margin: 0;
    font-size: 14px;
    color: var(--nx-text-secondary, #666666);
  }
}

.nx-layout-right {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  transition: width 0.3s ease;
}

.nx-layout-toggle-bar {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 40px;
  background: var(--nx-toggle-bg, #ffffff);
  border: 1px solid var(--nx-border-color, #e0e0e0);
  border-left: none;
  border-radius: 0 8px 8px 0;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 2px;
  z-index: 10;
  transition: all 0.2s ease;

  &:hover {
    background: var(--nx-toggle-hover-bg, #f5f5f5);
  }

  &--collapsed {
    left: -1px;
  }

  &__top,
  &__bottom {
    width: 8px;
    height: 1px;
    background: var(--nx-toggle-color, #666666);
    transition: all 0.2s ease;
  }

  &--collapsed &__top {
    transform: rotate(45deg) translateY(1px);
  }

  &--collapsed &__bottom {
    transform: rotate(-45deg) translateY(-1px);
  }
}

.nx-tab-menu {
  height: 40px;
  background: var(--nx-tab-bg, #ffffff);
  border-bottom: 1px solid var(--nx-border-color, #e0e0e0);
  display: flex;
  align-items: center;
  padding: 0 16px;
  overflow-x: auto;
  
  .tab-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: var(--nx-tab-item-bg, #f5f5f5);
    border: 1px solid var(--nx-border-color, #e0e0e0);
    border-radius: 4px 4px 0 0;
    font-size: 14px;
    color: var(--nx-text-color, #333333);
    cursor: pointer;
    user-select: none;
    
    &.active {
      background: var(--nx-tab-active-bg, #ffffff);
      border-bottom-color: transparent;
    }
    
    .tab-close {
      width: 16px;
      height: 16px;
      border: none;
      background: transparent;
      color: var(--nx-text-secondary, #666666);
      cursor: pointer;
      border-radius: 2px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      
      &:hover {
        background: var(--nx-close-hover-bg, #ff5f57);
        color: white;
      }
    }
  }
}

.nx-content {
  flex: 1;
  overflow: hidden;
  background: var(--nx-content-bg, #ffffff);
}

// 主题样式
:global([nx-theme="dark"]) {
  .nx-layout-wrapper {
    --nx-sidebar-bg: #252526;
    --nx-border-color: #404040;
    --nx-text-color: #ffffff;
    --nx-text-secondary: #cccccc;
    --nx-toggle-bg: #2d2d2d;
    --nx-toggle-hover-bg: #404040;
    --nx-toggle-color: #cccccc;
    --nx-tab-bg: #2d2d2d;
    --nx-tab-item-bg: #404040;
    --nx-tab-active-bg: #1e1e1e;
    --nx-content-bg: #1e1e1e;
    --nx-close-hover-bg: #ff5f57;
  }
}

:global([nx-theme="pink"]) {
  .nx-layout-wrapper {
    --nx-sidebar-bg: #fdf2f8;
    --nx-border-color: #f9a8d4;
    --nx-text-color: #831843;
    --nx-text-secondary: #be185d;
    --nx-toggle-bg: #fce7f3;
    --nx-toggle-hover-bg: #f3e8ff;
    --nx-toggle-color: #be185d;
    --nx-tab-bg: #fce7f3;
    --nx-tab-item-bg: #f9a8d4;
    --nx-tab-active-bg: #fdf2f8;
    --nx-content-bg: #fdf2f8;
    --nx-close-hover-bg: #ec4899;
  }
}
</style>
