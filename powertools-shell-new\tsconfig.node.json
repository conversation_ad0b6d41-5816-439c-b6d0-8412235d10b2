{"extends": "@electron-toolkit/tsconfig/tsconfig.node.json", "include": ["electron.vite.config.*", "src/main/**/*", "src/preload/**/*", "src/shared/**/*"], "compilerOptions": {"composite": true, "target": "ES2020", "lib": ["ES2020", "DOM"], "module": "CommonJS", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "types": ["node", "electron"], "baseUrl": ".", "paths": {"@shared/*": ["src/shared/*"], "@main/*": ["src/main/*"], "@preload/*": ["src/preload/*"]}}}