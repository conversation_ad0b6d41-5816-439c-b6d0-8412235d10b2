/**
 * 第三方库类型声明
 * 为没有类型定义的第三方库提供类型声明
 */

// ============= XTerm.js 类型声明 =============

declare module 'xterm' {
  export interface ITerminalOptions {
    cols?: number
    rows?: number
    cursorBlink?: boolean
    cursorStyle?: 'block' | 'underline' | 'bar'
    cursorWidth?: number
    bellStyle?: 'none' | 'visual' | 'sound' | 'both'
    fontSize?: number
    fontFamily?: string
    fontWeight?: string
    fontWeightBold?: string
    lineHeight?: number
    letterSpacing?: number
    scrollback?: number
    tabStopWidth?: number
    theme?: ITheme
    allowTransparency?: boolean
    altClickMovesCursor?: boolean
    convertEol?: boolean
    disableStdin?: boolean
    macOptionIsMeta?: boolean
    macOptionClickForcesSelection?: boolean
    minimumContrastRatio?: number
    rightClickSelectsWord?: boolean
    screenReaderMode?: boolean
    smoothScrollDuration?: number
    windowsMode?: boolean
    wordSeparator?: string
  }

  export interface ITheme {
    foreground?: string
    background?: string
    cursor?: string
    cursorAccent?: string
    selection?: string
    black?: string
    red?: string
    green?: string
    yellow?: string
    blue?: string
    magenta?: string
    cyan?: string
    white?: string
    brightBlack?: string
    brightRed?: string
    brightGreen?: string
    brightYellow?: string
    brightBlue?: string
    brightMagenta?: string
    brightCyan?: string
    brightWhite?: string
  }

  export interface IDisposable {
    dispose(): void
  }

  export class Terminal {
    constructor(options?: ITerminalOptions)
    
    element: HTMLElement | undefined
    textarea: HTMLTextAreaElement | undefined
    rows: number
    cols: number
    buffer: any
    markers: any
    parser: any
    unicode: any
    
    blur(): void
    focus(): void
    resize(columns: number, rows: number): void
    write(data: string | Uint8Array, callback?: () => void): void
    writeln(data: string, callback?: () => void): void
    paste(data: string): void
    open(parent: HTMLElement): void
    attachCustomKeyEventHandler(customKeyEventHandler: (event: KeyboardEvent) => boolean): void
    registerLinkMatcher(regex: RegExp, handler: (event: MouseEvent, uri: string) => void, options?: any): number
    deregisterLinkMatcher(matcherId: number): void
    registerCharacterJoiner(handler: (text: string) => [number, number][]): number
    deregisterCharacterJoiner(joinerId: number): void
    addMarker(cursorYOffset: number): any
    hasSelection(): boolean
    getSelection(): string
    getSelectionPosition(): any
    clearSelection(): void
    select(column: number, row: number, length: number): void
    selectAll(): void
    selectLines(start: number, end: number): void
    dispose(): void
    scrollLines(amount: number): void
    scrollPages(pageCount: number): void
    scrollToTop(): void
    scrollToBottom(): void
    scrollToLine(line: number): void
    clear(): void
    reset(): void
    loadAddon(addon: any): void
    
    onCursorMove: IEvent<void>
    onData: IEvent<string>
    onKey: IEvent<{ key: string; domEvent: KeyboardEvent }>
    onLineFeed: IEvent<void>
    onScroll: IEvent<number>
    onSelectionChange: IEvent<void>
    onRender: IEvent<{ start: number; end: number }>
    onResize: IEvent<{ cols: number; rows: number }>
    onTitleChange: IEvent<string>
  }

  export interface IEvent<T> {
    (listener: (arg: T) => any): IDisposable
  }
}

// ============= XTerm 插件类型声明 =============

declare module 'xterm-addon-fit' {
  export class FitAddon {
    constructor()
    activate(terminal: any): void
    dispose(): void
    fit(): void
    proposeDimensions(): { cols: number; rows: number } | undefined
  }
}

declare module 'xterm-addon-search' {
  export interface ISearchOptions {
    regex?: boolean
    wholeWord?: boolean
    caseSensitive?: boolean
    incremental?: boolean
  }

  export interface ISearchResult {
    term: string
    col: number
    row: number
  }

  export class SearchAddon {
    constructor()
    activate(terminal: any): void
    dispose(): void
    findNext(term: string, searchOptions?: ISearchOptions): boolean
    findPrevious(term: string, searchOptions?: ISearchOptions): boolean
  }
}

declare module 'xterm-addon-web-links' {
  export class WebLinksAddon {
    constructor(handler?: (event: MouseEvent, uri: string) => void, options?: any)
    activate(terminal: any): void
    dispose(): void
  }
}

declare module 'xterm-addon-webgl' {
  export class WebglAddon {
    constructor(preserveDrawingBuffer?: boolean)
    activate(terminal: any): void
    dispose(): void
    clearTextureAtlas(): void
  }
}

// ============= Node-pty 类型声明 =============

declare module 'node-pty' {
  export interface IPtyForkOptions {
    name?: string
    cols?: number
    rows?: number
    cwd?: string
    env?: { [key: string]: string }
    encoding?: string
    handleFlowControl?: boolean
    flowControlPause?: string
    flowControlResume?: string
  }

  export interface IPtyProcess {
    pid: number
    process: string
    handleFlowControl: boolean
    
    write(data: string): void
    resize(cols: number, rows: number): void
    clear(): void
    kill(signal?: string): void
    
    onData(callback: (data: string) => void): void
    onExit(callback: (exitCode: number, signal?: number) => void): void
  }

  export function spawn(file: string, args: string[] | string, options: IPtyForkOptions): IPtyProcess
  export function fork(file: string, args: string[] | string, options: IPtyForkOptions): IPtyProcess
}

// ============= SSH2 类型声明 =============

declare module 'nxshell-ssh2' {
  export interface ConnectConfig {
    host: string
    port?: number
    username: string
    password?: string
    privateKey?: Buffer | string
    passphrase?: string
    localHostname?: string
    localUsername?: string
    tryKeyboard?: boolean
    keepaliveInterval?: number
    keepaliveCountMax?: number
    readyTimeout?: number
    sock?: any
    strictVendor?: boolean
    algorithms?: any
    compress?: boolean | string
    debug?: (information: string) => any
  }

  export interface ExecOptions {
    env?: { [key: string]: any }
    pty?: boolean | {
      rows?: number
      cols?: number
      height?: number
      width?: number
      term?: string
    }
    x11?: boolean | {
      single?: boolean
      screen?: number
      protocol?: string
      cookie?: string
    }
  }

  export class Client {
    connect(config: ConnectConfig): Client
    exec(command: string, options: ExecOptions, callback: (err: Error | undefined, stream: any) => void): Client
    exec(command: string, callback: (err: Error | undefined, stream: any) => void): Client
    shell(window: any, options: any, callback: (err: Error | undefined, stream: any) => void): Client
    shell(options: any, callback: (err: Error | undefined, stream: any) => void): Client
    shell(callback: (err: Error | undefined, stream: any) => void): Client
    sftp(callback: (err: Error | undefined, sftp: any) => void): Client
    subsys(name: string, callback: (err: Error | undefined, stream: any) => void): Client
    forwardIn(bindAddr: string, bindPort: number, callback: (err: Error | undefined, port: number) => void): Client
    unforwardIn(bindAddr: string, bindPort: number, callback: (err: Error | undefined) => void): Client
    forwardOut(srcIP: string, srcPort: number, dstIP: string, dstPort: number, callback: (err: Error | undefined, stream: any) => void): Client
    openssh_noMoreSessions(callback: (err: Error | undefined) => void): Client
    openssh_forwardInStreamLocal(socketPath: string, callback: (err: Error | undefined) => void): Client
    openssh_unforwardInStreamLocal(socketPath: string, callback: (err: Error | undefined) => void): Client
    openssh_forwardOutStreamLocal(socketPath: string, callback: (err: Error | undefined, stream: any) => void): Client
    end(): Client
    destroy(): Client
    
    on(event: 'banner', listener: (message: string, language: string) => void): this
    on(event: 'ready', listener: () => void): this
    on(event: 'tcp connection', listener: (details: any, accept: () => any, reject: () => void) => void): this
    on(event: 'x11', listener: (details: any, accept: () => any, reject: () => void) => void): this
    on(event: 'keyboard-interactive', listener: (name: string, instructions: string, instructionsLang: string, prompts: any[], finish: (responses: string[]) => void) => void): this
    on(event: 'change password', listener: (message: string, language: string, done: (password: string) => void) => void): this
    on(event: 'continue', listener: () => void): this
    on(event: 'error', listener: (err: any) => void): this
    on(event: 'end', listener: () => void): this
    on(event: 'close', listener: (hadError: boolean) => void): this
    on(event: string, listener: (...args: any[]) => void): this
  }
}

// ============= VNC 类型声明 =============

declare module 'nxshell-vnc' {
  export interface VNCOptions {
    host: string
    port?: number
    password?: string
    quality?: number
    compression?: number
    viewOnly?: boolean
    shared?: boolean
    localCursor?: boolean
    dotCursor?: boolean
    scaling?: boolean
    clipboardUp?: boolean
    clipboardDown?: boolean
  }

  export class VNCClient {
    constructor(options: VNCOptions)
    connect(): Promise<void>
    disconnect(): void
    sendKey(key: number, down: boolean): void
    sendPointer(x: number, y: number, mask: number): void
    sendClipboard(text: string): void
    
    on(event: 'connect', listener: () => void): this
    on(event: 'disconnect', listener: () => void): this
    on(event: 'error', listener: (error: Error) => void): this
    on(event: 'rect', listener: (rect: any) => void): this
    on(event: 'clipboard', listener: (text: string) => void): this
    on(event: string, listener: (...args: any[]) => void): this
  }
}

// ============= FTP 类型声明 =============

declare module 'nxshell-ftp' {
  export interface FTPOptions {
    host: string
    port?: number
    user?: string
    password?: string
    secure?: boolean
    secureOptions?: any
    connTimeout?: number
    pasvTimeout?: number
    keepalive?: number
  }

  export interface FileInfo {
    type: string
    name: string
    size: number
    date: Date
    rights: {
      user: string
      group: string
      other: string
    }
    owner: string
    group: string
  }

  export class FTPClient {
    constructor()
    connect(options: FTPOptions): Promise<void>
    end(): void
    destroy(): void
    list(path?: string): Promise<FileInfo[]>
    get(path: string): Promise<NodeJS.ReadableStream>
    put(input: NodeJS.ReadableStream | Buffer | string, destPath: string): Promise<void>
    mkdir(path: string, recursive?: boolean): Promise<void>
    rmdir(path: string, recursive?: boolean): Promise<void>
    delete(path: string): Promise<void>
    rename(oldPath: string, newPath: string): Promise<void>
    size(path: string): Promise<number>
    lastMod(path: string): Promise<Date>
    restart(byteOffset: number): Promise<void>
    
    on(event: 'greeting', listener: (msg: string) => void): this
    on(event: 'ready', listener: () => void): this
    on(event: 'close', listener: (hadError: boolean) => void): this
    on(event: 'end', listener: () => void): this
    on(event: 'error', listener: (err: Error) => void): this
    on(event: string, listener: (...args: any[]) => void): this
  }
}

// ============= 串口类型声明 =============

declare module 'serialport' {
  export interface OpenOptions {
    baudRate: number
    dataBits?: 5 | 6 | 7 | 8
    stopBits?: 1 | 1.5 | 2
    parity?: 'none' | 'even' | 'mark' | 'odd' | 'space'
    rtscts?: boolean
    xon?: boolean
    xoff?: boolean
    xany?: boolean
    autoOpen?: boolean
    lock?: boolean
    highWaterMark?: number
  }

  export interface PortInfo {
    path: string
    manufacturer?: string
    serialNumber?: string
    pnpId?: string
    locationId?: string
    productId?: string
    vendorId?: string
  }

  export class SerialPort {
    constructor(path: string, options?: OpenOptions, callback?: (error: Error | null) => void)
    
    static list(): Promise<PortInfo[]>
    
    open(callback?: (error: Error | null) => void): void
    close(callback?: (error: Error | null) => void): void
    write(data: string | Buffer | number[], callback?: (error: Error | null, bytesWritten: number) => void): boolean
    read(size?: number): Buffer | string | null
    
    on(event: 'open', listener: () => void): this
    on(event: 'data', listener: (data: Buffer) => void): this
    on(event: 'close', listener: () => void): this
    on(event: 'error', listener: (err: Error) => void): this
    on(event: string, listener: (...args: any[]) => void): this
  }
}

// ============= 导出类型 =============

export type {
  // XTerm
  ITerminalOptions,
  ITheme,
  IDisposable,
  Terminal,
  IEvent,
  
  // Node-pty
  IPtyForkOptions,
  IPtyProcess,
  
  // SSH2
  ConnectConfig,
  ExecOptions,
  Client as SSH2Client,
  
  // VNC
  VNCOptions,
  VNCClient,
  
  // FTP
  FTPOptions,
  FileInfo as FTPFileInfo,
  FTPClient,
  
  // SerialPort
  OpenOptions as SerialPortOptions,
  PortInfo,
  SerialPort
} from 'xterm'
