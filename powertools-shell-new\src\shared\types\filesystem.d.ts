/**
 * 文件系统相关类型定义
 * 定义文件管理、传输、存储等相关类型
 */

// ============= 基础文件系统类型 =============

export type FileType = 'file' | 'directory' | 'symlink' | 'block' | 'character' | 'fifo' | 'socket'

export type FilePermission = 'r' | 'w' | 'x' | '-'

export interface FilePermissions {
  owner: {
    read: boolean
    write: boolean
    execute: boolean
  }
  group: {
    read: boolean
    write: boolean
    execute: boolean
  }
  others: {
    read: boolean
    write: boolean
    execute: boolean
  }
  octal: string
  symbolic: string
}

export interface FileEntry {
  name: string
  path: string
  type: FileType
  size: number
  modified: Date
  accessed?: Date
  created?: Date
  permissions: FilePermissions
  owner?: string
  group?: string
  isHidden: boolean
  isSymlink: boolean
  linkTarget?: string
  mimeType?: string
  extension?: string
  icon?: string
}

export interface DirectoryEntry extends FileEntry {
  type: 'directory'
  children?: FileEntry[]
  isExpanded?: boolean
  childCount?: number
}

// ============= 文件操作接口 =============

export interface FileOperation {
  id: string
  type: 'copy' | 'move' | 'delete' | 'rename' | 'create' | 'upload' | 'download'
  source: string | string[]
  destination?: string
  progress: number
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled'
  startTime: Date
  endTime?: Date
  error?: Error
  totalSize?: number
  transferredSize?: number
  speed?: number
  estimatedTime?: number
}

export interface FileOperationOptions {
  overwrite?: boolean
  preserveTimestamp?: boolean
  preservePermissions?: boolean
  recursive?: boolean
  followSymlinks?: boolean
  createDirectories?: boolean
  skipExisting?: boolean
  retryOnError?: boolean
  maxRetries?: number
}

// ============= 文件传输接口 =============

export interface TransferProgress {
  operationId: string
  filename: string
  totalSize: number
  transferredSize: number
  progress: number
  speed: number
  estimatedTime: number
  status: 'transferring' | 'completed' | 'failed' | 'paused'
}

export interface TransferManager {
  // 基础传输
  upload(localPath: string, remotePath: string, options?: FileOperationOptions): Promise<string>
  download(remotePath: string, localPath: string, options?: FileOperationOptions): Promise<string>
  
  // 批量传输
  uploadMultiple(files: Array<{ local: string; remote: string }>, options?: FileOperationOptions): Promise<string[]>
  downloadMultiple(files: Array<{ remote: string; local: string }>, options?: FileOperationOptions): Promise<string[]>
  
  // 传输控制
  pauseTransfer(operationId: string): Promise<void>
  resumeTransfer(operationId: string): Promise<void>
  cancelTransfer(operationId: string): Promise<void>
  
  // 进度监控
  getTransferProgress(operationId: string): TransferProgress | null
  getAllTransfers(): TransferProgress[]
  
  // 事件监听
  onProgress(listener: (progress: TransferProgress) => void): void
  onComplete(listener: (operationId: string) => void): void
  onError(listener: (operationId: string, error: Error) => void): void
}

// ============= 文件系统服务接口 =============

export interface FileSystemService {
  // 目录操作
  listDirectory(path: string): Promise<FileEntry[]>
  createDirectory(path: string, recursive?: boolean): Promise<void>
  removeDirectory(path: string, recursive?: boolean): Promise<void>
  
  // 文件操作
  readFile(path: string): Promise<Buffer>
  writeFile(path: string, data: Buffer | string): Promise<void>
  deleteFile(path: string): Promise<void>
  copyFile(source: string, destination: string): Promise<void>
  moveFile(source: string, destination: string): Promise<void>
  renameFile(oldPath: string, newPath: string): Promise<void>
  
  // 文件信息
  stat(path: string): Promise<FileEntry>
  exists(path: string): Promise<boolean>
  isFile(path: string): Promise<boolean>
  isDirectory(path: string): Promise<boolean>
  
  // 权限管理
  chmod(path: string, permissions: string | number): Promise<void>
  chown(path: string, owner: string, group?: string): Promise<void>
  
  // 路径操作
  resolvePath(path: string): string
  joinPath(...paths: string[]): string
  dirname(path: string): string
  basename(path: string): string
  extname(path: string): string
  
  // 搜索
  search(pattern: string, directory: string, options?: SearchOptions): Promise<FileEntry[]>
  
  // 监听文件变化
  watchFile(path: string, listener: (event: FileWatchEvent) => void): void
  unwatchFile(path: string): void
}

export interface SearchOptions {
  recursive?: boolean
  includeHidden?: boolean
  fileTypes?: FileType[]
  maxResults?: number
  caseSensitive?: boolean
  useRegex?: boolean
}

export interface FileWatchEvent {
  type: 'created' | 'modified' | 'deleted' | 'renamed'
  path: string
  oldPath?: string
  timestamp: Date
}

// ============= 本地文件系统接口 =============

export interface LocalFileSystem extends FileSystemService {
  // 本地特有功能
  openFile(path: string): Promise<void>
  showInExplorer(path: string): Promise<void>
  getSystemInfo(): Promise<SystemInfo>
  getDrives(): Promise<DriveInfo[]>
  getRecentFiles(): Promise<string[]>
  addToRecentFiles(path: string): Promise<void>
}

export interface SystemInfo {
  platform: string
  arch: string
  homedir: string
  tmpdir: string
  hostname: string
  username: string
  shell?: string
}

export interface DriveInfo {
  name: string
  path: string
  type: 'fixed' | 'removable' | 'network' | 'cdrom' | 'ram'
  totalSize: number
  freeSize: number
  usedSize: number
  label?: string
  filesystem?: string
}

// ============= 远程文件系统接口 =============

export interface RemoteFileSystem extends FileSystemService {
  // 连接管理
  connect(): Promise<void>
  disconnect(): Promise<void>
  isConnected(): boolean
  
  // 远程特有功能
  getWorkingDirectory(): Promise<string>
  setWorkingDirectory(path: string): Promise<void>
  getHomeDirectory(): Promise<string>
  
  // 传输管理
  getTransferManager(): TransferManager
}

// ============= 文件编辑器接口 =============

export interface FileEditor {
  // 基础操作
  open(path: string): Promise<void>
  save(): Promise<void>
  saveAs(path: string): Promise<void>
  close(): Promise<void>
  
  // 内容操作
  getContent(): string
  setContent(content: string): void
  insertText(text: string, position?: number): void
  replaceText(start: number, end: number, text: string): void
  
  // 编辑状态
  isDirty(): boolean
  canUndo(): boolean
  canRedo(): boolean
  undo(): void
  redo(): void
  
  // 搜索替换
  find(pattern: string, options?: FindOptions): number[]
  replace(pattern: string, replacement: string, options?: ReplaceOptions): number
  
  // 事件
  onContentChange(listener: (content: string) => void): void
  onSave(listener: (path: string) => void): void
  onClose(listener: () => void): void
}

export interface FindOptions {
  caseSensitive?: boolean
  wholeWord?: boolean
  useRegex?: boolean
  backwards?: boolean
}

export interface ReplaceOptions extends FindOptions {
  replaceAll?: boolean
}

// ============= 文件管理器接口 =============

export interface FileManager {
  // 文件系统管理
  getLocalFileSystem(): LocalFileSystem
  getRemoteFileSystem(sessionId: string): RemoteFileSystem | null
  createRemoteFileSystem(sessionId: string, config: any): Promise<RemoteFileSystem>
  
  // 编辑器管理
  openEditor(path: string, sessionId?: string): Promise<FileEditor>
  getEditor(path: string): FileEditor | null
  closeEditor(path: string): Promise<void>
  
  // 操作管理
  getOperations(): FileOperation[]
  getOperation(id: string): FileOperation | null
  cancelOperation(id: string): Promise<void>
  
  // 收藏夹
  addBookmark(path: string, name?: string): Promise<void>
  removeBookmark(path: string): Promise<void>
  getBookmarks(): Promise<Array<{ name: string; path: string }>>
  
  // 历史记录
  getHistory(): string[]
  addToHistory(path: string): void
  clearHistory(): void
  
  // 事件
  on(event: string, listener: (...args: any[]) => void): void
  off(event: string, listener: (...args: any[]) => void): void
}

// ============= 导出类型 =============

export type {
  FileType,
  FilePermission,
  FilePermissions,
  FileEntry,
  DirectoryEntry,
  FileOperation,
  FileOperationOptions,
  TransferProgress,
  TransferManager,
  FileSystemService,
  SearchOptions,
  FileWatchEvent,
  LocalFileSystem,
  SystemInfo,
  DriveInfo,
  RemoteFileSystem,
  FileEditor,
  FindOptions,
  ReplaceOptions,
  FileManager
}
