/**
 * Vue 相关类型定义
 * 扩展Vue的类型定义，添加PowerTools特有的属性和方法
 */

import type { PowerToolsAPI } from '@shared/types'

// ============= Vue 全局属性扩展 =============

declare module '@vue/runtime-core' {
  interface ComponentCustomProperties {
    $powertools: PowerToolsAPI
    $t: (key: string, ...args: any[]) => string
    $tc: (key: string, choice?: number, ...args: any[]) => string
    $te: (key: string) => boolean
    $d: (value: Date | number, ...args: any[]) => string
    $n: (value: number, ...args: any[]) => string
  }
}

// ============= 路由元信息类型 =============

declare module 'vue-router' {
  interface RouteMeta {
    title?: string
    icon?: string
    requiresAuth?: boolean
    keepAlive?: boolean
    showInMenu?: boolean
    order?: number
    permissions?: string[]
    sessionType?: string
  }
}

// ============= Pinia Store 类型扩展 =============

declare module 'pinia' {
  export interface PiniaCustomProperties {
    $powertools: PowerToolsAPI
    $router: import('vue-router').Router
    $route: import('vue-router').RouteLocationNormalizedLoaded
  }
}

// ============= 组件 Props 类型 =============

export interface BaseComponentProps {
  id?: string
  class?: string | string[] | Record<string, boolean>
  style?: string | Record<string, any>
}

export interface SessionComponentProps extends BaseComponentProps {
  sessionId: string
  sessionType?: string
  config?: Record<string, any>
}

export interface TerminalComponentProps extends SessionComponentProps {
  theme?: string
  fontSize?: number
  fontFamily?: string
  readonly?: boolean
  autoFocus?: boolean
}

export interface FileManagerComponentProps extends SessionComponentProps {
  currentPath?: string
  showHidden?: boolean
  viewMode?: 'list' | 'grid' | 'tree'
  allowMultiSelect?: boolean
}

export interface EditorComponentProps extends BaseComponentProps {
  filePath?: string
  content?: string
  language?: string
  theme?: string
  readonly?: boolean
  autoSave?: boolean
}

// ============= 组件 Emits 类型 =============

export interface BaseComponentEmits {
  'update:modelValue': [value: any]
  'change': [value: any]
  'focus': [event: FocusEvent]
  'blur': [event: FocusEvent]
}

export interface SessionComponentEmits extends BaseComponentEmits {
  'session-created': [sessionId: string]
  'session-connected': [sessionId: string]
  'session-disconnected': [sessionId: string]
  'session-error': [sessionId: string, error: Error]
}

export interface TerminalComponentEmits extends SessionComponentEmits {
  'data': [data: string]
  'resize': [cols: number, rows: number]
  'title-change': [title: string]
  'selection-change': [selection: string]
}

export interface FileManagerComponentEmits extends BaseComponentEmits {
  'path-change': [path: string]
  'file-select': [files: string[]]
  'file-open': [file: string]
  'file-delete': [files: string[]]
  'file-rename': [oldName: string, newName: string]
}

export interface EditorComponentEmits extends BaseComponentEmits {
  'content-change': [content: string]
  'save': [filePath: string, content: string]
  'close': []
}

// ============= 组件实例类型 =============

export interface ComponentInstance {
  $el: HTMLElement
  $props: Record<string, any>
  $emit: (event: string, ...args: any[]) => void
  $slots: Record<string, any>
  $attrs: Record<string, any>
  $refs: Record<string, any>
}

export interface SessionComponentInstance extends ComponentInstance {
  sessionId: string
  connect(): Promise<void>
  disconnect(): Promise<void>
  reconnect(): Promise<void>
  getStatus(): string
}

export interface TerminalComponentInstance extends SessionComponentInstance {
  terminal: any
  write(data: string): void
  clear(): void
  focus(): void
  resize(cols: number, rows: number): void
  getSelection(): string
}

export interface FileManagerComponentInstance extends ComponentInstance {
  currentPath: string
  selectedFiles: string[]
  refresh(): Promise<void>
  navigateTo(path: string): Promise<void>
  selectFile(file: string): void
  selectAll(): void
  clearSelection(): void
}

export interface EditorComponentInstance extends ComponentInstance {
  content: string
  isDirty: boolean
  save(): Promise<void>
  undo(): void
  redo(): void
  find(pattern: string): void
  replace(pattern: string, replacement: string): void
}

// ============= 表单相关类型 =============

export interface FormField {
  name: string
  label: string
  type: 'text' | 'password' | 'number' | 'select' | 'checkbox' | 'radio' | 'textarea' | 'file'
  value?: any
  placeholder?: string
  required?: boolean
  disabled?: boolean
  readonly?: boolean
  options?: Array<{ label: string; value: any }>
  validation?: {
    rules: Array<{
      required?: boolean
      min?: number
      max?: number
      pattern?: RegExp
      validator?: (value: any) => boolean | string
    }>
    message?: string
  }
}

export interface FormData {
  [key: string]: any
}

export interface FormValidation {
  valid: boolean
  errors: Record<string, string[]>
}

// ============= 对话框类型 =============

export interface DialogOptions {
  title?: string
  message?: string
  type?: 'info' | 'success' | 'warning' | 'error' | 'confirm'
  showCancelButton?: boolean
  confirmButtonText?: string
  cancelButtonText?: string
  showInput?: boolean
  inputPlaceholder?: string
  inputValue?: string
  inputValidator?: (value: string) => boolean | string
}

export interface DialogResult {
  action: 'confirm' | 'cancel'
  value?: string
}

// ============= 通知类型 =============

export interface NotificationOptions {
  title?: string
  message: string
  type?: 'info' | 'success' | 'warning' | 'error'
  duration?: number
  showClose?: boolean
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left'
  onClick?: () => void
  onClose?: () => void
}

// ============= 菜单类型 =============

export interface MenuItem {
  id: string
  label: string
  icon?: string
  disabled?: boolean
  visible?: boolean
  children?: MenuItem[]
  action?: () => void
  shortcut?: string
  separator?: boolean
}

export interface ContextMenuOptions {
  items: MenuItem[]
  x: number
  y: number
  minWidth?: number
  maxWidth?: number
}

// ============= 主题类型 =============

export interface ThemeColors {
  primary: string
  secondary: string
  success: string
  warning: string
  error: string
  info: string
  background: string
  surface: string
  text: string
  textSecondary: string
  border: string
  shadow: string
}

export interface ThemeConfig {
  name: string
  type: 'light' | 'dark'
  colors: ThemeColors
  fonts: {
    family: string
    size: number
    weight: number
  }
  spacing: {
    xs: number
    sm: number
    md: number
    lg: number
    xl: number
  }
  borderRadius: number
  animation: {
    duration: number
    easing: string
  }
}

// ============= 工具函数类型 =============

export type VueComponent = import('vue').Component
export type VueRef<T = any> = import('vue').Ref<T>
export type VueComputed<T = any> = import('vue').ComputedRef<T>
export type VueReactive<T = any> = import('vue').UnwrapNestedRefs<T>

// ============= 导出类型 =============

export type {
  BaseComponentProps,
  SessionComponentProps,
  TerminalComponentProps,
  FileManagerComponentProps,
  EditorComponentProps,
  BaseComponentEmits,
  SessionComponentEmits,
  TerminalComponentEmits,
  FileManagerComponentEmits,
  EditorComponentEmits,
  ComponentInstance,
  SessionComponentInstance,
  TerminalComponentInstance,
  FileManagerComponentInstance,
  EditorComponentInstance,
  FormField,
  FormData,
  FormValidation,
  DialogOptions,
  DialogResult,
  NotificationOptions,
  MenuItem,
  ContextMenuOptions,
  ThemeColors,
  ThemeConfig
}
