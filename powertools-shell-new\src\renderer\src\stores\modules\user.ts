/**
 * 用户状态管理
 * 迁移自原有的 user store
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export interface IUser {
  avatar: string
  userName: string
  email?: string
  userId?: string
  loginTime?: Date
  lastActiveTime?: Date
}

export const useUserStore = defineStore('user', () => {
  // 状态
  const avatar = ref<string>('')
  const userName = ref<string>('')
  const email = ref<string>('')
  const userId = ref<string>('')
  const loginTime = ref<Date | null>(null)
  const lastActiveTime = ref<Date | null>(null)
  const isLoggedIn = ref<boolean>(false)

  // 计算属性
  const userDisplayName = computed(() => {
    return userName.value || email.value || 'Anonymous'
  })

  const hasAvatar = computed(() => {
    return avatar.value && avatar.value.length > 0
  })

  const loginDuration = computed(() => {
    if (!loginTime.value) return 0
    return Date.now() - loginTime.value.getTime()
  })

  // 动作
  const login = (userInfo: Partial<IUser>) => {
    if (userInfo.userName) userName.value = userInfo.userName
    if (userInfo.avatar) avatar.value = userInfo.avatar
    if (userInfo.email) email.value = userInfo.email
    if (userInfo.userId) userId.value = userInfo.userId
    
    loginTime.value = new Date()
    lastActiveTime.value = new Date()
    isLoggedIn.value = true
    
    console.log('User logged in:', userDisplayName.value)
  }

  const logout = () => {
    avatar.value = ''
    userName.value = ''
    email.value = ''
    userId.value = ''
    loginTime.value = null
    lastActiveTime.value = null
    isLoggedIn.value = false
    
    console.log('User logged out')
  }

  const updateProfile = (updates: Partial<IUser>) => {
    if (updates.userName !== undefined) userName.value = updates.userName
    if (updates.avatar !== undefined) avatar.value = updates.avatar
    if (updates.email !== undefined) email.value = updates.email
    
    updateLastActiveTime()
    console.log('User profile updated')
  }

  const updateAvatar = (newAvatar: string) => {
    avatar.value = newAvatar
    updateLastActiveTime()
    console.log('User avatar updated')
  }

  const updateLastActiveTime = () => {
    lastActiveTime.value = new Date()
  }

  const getUserInfo = (): IUser => {
    return {
      avatar: avatar.value,
      userName: userName.value,
      email: email.value,
      userId: userId.value,
      loginTime: loginTime.value || undefined,
      lastActiveTime: lastActiveTime.value || undefined
    }
  }

  const clearUserData = () => {
    avatar.value = ''
    userName.value = ''
    email.value = ''
    userId.value = ''
    loginTime.value = null
    lastActiveTime.value = null
    isLoggedIn.value = false
  }

  // 持久化相关
  const saveToStorage = () => {
    try {
      const userData = {
        avatar: avatar.value,
        userName: userName.value,
        email: email.value,
        userId: userId.value,
        loginTime: loginTime.value?.toISOString(),
        lastActiveTime: lastActiveTime.value?.toISOString(),
        isLoggedIn: isLoggedIn.value
      }
      
      localStorage.setItem('powertools-user', JSON.stringify(userData))
      console.log('User data saved to storage')
      
    } catch (error) {
      console.error('Failed to save user data:', error)
    }
  }

  const loadFromStorage = () => {
    try {
      const stored = localStorage.getItem('powertools-user')
      if (stored) {
        const userData = JSON.parse(stored)
        
        avatar.value = userData.avatar || ''
        userName.value = userData.userName || ''
        email.value = userData.email || ''
        userId.value = userData.userId || ''
        isLoggedIn.value = userData.isLoggedIn || false
        
        if (userData.loginTime) {
          loginTime.value = new Date(userData.loginTime)
        }
        if (userData.lastActiveTime) {
          lastActiveTime.value = new Date(userData.lastActiveTime)
        }
        
        console.log('User data loaded from storage')
      }
    } catch (error) {
      console.error('Failed to load user data:', error)
    }
  }

  const clearStorage = () => {
    try {
      localStorage.removeItem('powertools-user')
      console.log('User data cleared from storage')
    } catch (error) {
      console.error('Failed to clear user data:', error)
    }
  }

  // 初始化时从存储加载数据
  loadFromStorage()

  return {
    // 状态
    avatar,
    userName,
    email,
    userId,
    loginTime,
    lastActiveTime,
    isLoggedIn,
    
    // 计算属性
    userDisplayName,
    hasAvatar,
    loginDuration,
    
    // 动作
    login,
    logout,
    updateProfile,
    updateAvatar,
    updateLastActiveTime,
    getUserInfo,
    clearUserData,
    saveToStorage,
    loadFromStorage,
    clearStorage
  }
})

export default useUserStore
