/**
 * 全局设置服务
 * 处理应用配置的读取和保存
 */

// 默认配置
const defaultProfiles: Record<string, any> = {
  xterm: {
    theme: 'light',
    fontSize: 14,
    fontFamily: 'DejaVu Sans Mono, Monaco, Consolas, monospace',
    cursorBlink: true,
    cursorStyle: 'block',
    scrollback: 1000,
    bellStyle: 'none'
  },
  app: {
    language: 'zh-CN',
    autoUpdate: true,
    startupCheck: true,
    windowState: {
      width: 1200,
      height: 800,
      maximized: false
    }
  },
  security: {
    rememberPasswords: false,
    autoLock: false,
    lockTimeout: 300000 // 5分钟
  }
}

/**
 * 获取配置文件
 */
export async function getProfile(profileName: string): Promise<any> {
  try {
    if (typeof window.powertools !== 'undefined') {
      const service = window.powertools.getService('storage')
      const profile = await service.read(`profile-${profileName}`)
      
      // 如果没有配置，返回默认配置
      if (!profile) {
        return defaultProfiles[profileName] || {}
      }
      
      // 合并默认配置和用户配置
      return {
        ...defaultProfiles[profileName],
        ...profile
      }
    } else {
      // 开发模式下返回默认配置
      console.warn('PowerTools API not available, using default profile')
      return defaultProfiles[profileName] || {}
    }
  } catch (error) {
    console.error(`Failed to get profile ${profileName}:`, error)
    return defaultProfiles[profileName] || {}
  }
}

/**
 * 更新配置文件
 */
export async function updateProfile(profileName: string, updates: any): Promise<void> {
  try {
    if (typeof window.powertools !== 'undefined') {
      const service = window.powertools.getService('storage')
      
      // 获取当前配置
      const currentProfile = await getProfile(profileName)
      
      // 合并更新
      const updatedProfile = {
        ...currentProfile,
        ...updates
      }
      
      // 保存配置
      await service.save(`profile-${profileName}`, updatedProfile)
      
      console.log(`Profile ${profileName} updated successfully`)
    } else {
      console.warn('PowerTools API not available, profile update skipped')
    }
  } catch (error) {
    console.error(`Failed to update profile ${profileName}:`, error)
    throw error
  }
}

/**
 * 删除配置文件
 */
export async function deleteProfile(profileName: string): Promise<void> {
  try {
    if (typeof window.powertools !== 'undefined') {
      const service = window.powertools.getService('storage')
      await service.delete(`profile-${profileName}`)
      
      console.log(`Profile ${profileName} deleted successfully`)
    } else {
      console.warn('PowerTools API not available, profile deletion skipped')
    }
  } catch (error) {
    console.error(`Failed to delete profile ${profileName}:`, error)
    throw error
  }
}

/**
 * 重置配置文件到默认值
 */
export async function resetProfile(profileName: string): Promise<void> {
  try {
    const defaultProfile = defaultProfiles[profileName]
    if (defaultProfile) {
      await updateProfile(profileName, defaultProfile)
      console.log(`Profile ${profileName} reset to defaults`)
    } else {
      throw new Error(`No default profile found for ${profileName}`)
    }
  } catch (error) {
    console.error(`Failed to reset profile ${profileName}:`, error)
    throw error
  }
}

/**
 * 导出配置文件
 */
export async function exportProfile(profileName: string, exportPath: string): Promise<void> {
  try {
    if (typeof window.powertools !== 'undefined') {
      const service = window.powertools.getService('storage')
      await service.export(`profile-${profileName}`, exportPath)
      
      console.log(`Profile ${profileName} exported to ${exportPath}`)
    } else {
      throw new Error('PowerTools API not available')
    }
  } catch (error) {
    console.error(`Failed to export profile ${profileName}:`, error)
    throw error
  }
}

/**
 * 导入配置文件
 */
export async function importProfile(profileName: string, importPath: string): Promise<void> {
  try {
    if (typeof window.powertools !== 'undefined') {
      const service = window.powertools.getService('storage')
      await service.import(importPath, `profile-${profileName}`)
      
      console.log(`Profile ${profileName} imported from ${importPath}`)
    } else {
      throw new Error('PowerTools API not available')
    }
  } catch (error) {
    console.error(`Failed to import profile ${profileName}:`, error)
    throw error
  }
}

/**
 * 获取所有可用的配置文件名称
 */
export function getAvailableProfiles(): string[] {
  return Object.keys(defaultProfiles)
}

/**
 * 检查配置文件是否存在
 */
export async function profileExists(profileName: string): Promise<boolean> {
  try {
    const profile = await getProfile(profileName)
    return profile !== null
  } catch {
    return false
  }
}
