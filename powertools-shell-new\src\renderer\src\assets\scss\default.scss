/**
 * PowerTools Shell 默认样式
 * 全局样式和主题变量定义
 */

// ============= CSS 变量定义 =============

:root {
  // 基础颜色
  --nx-primary: #409eff;
  --nx-success: #67c23a;
  --nx-warning: #e6a23c;
  --nx-danger: #f56c6c;
  --nx-info: #909399;

  // 文本颜色
  --nx-text-primary: #303133;
  --nx-text-regular: #606266;
  --nx-text-secondary: #909399;
  --nx-text-placeholder: #c0c4cc;

  // 边框颜色
  --nx-border-darker: #cdd0d6;
  --nx-border-dark: #d4d7de;
  --nx-border-base: #dcdfe6;
  --nx-border-light: #e4e7ed;
  --nx-border-lighter: #ebeef5;
  --nx-border-extra-light: #f2f6fc;

  // 背景颜色
  --nx-bg-color: #ffffff;
  --nx-bg-color-page: #f2f3f5;
  --nx-bg-color-overlay: #ffffff;

  // 填充颜色
  --nx-fill-color-darker: #e6e8eb;
  --nx-fill-color-dark: #ebedf0;
  --nx-fill-color: #f0f2f5;
  --nx-fill-color-light: #f5f7fa;
  --nx-fill-color-lighter: #fafafa;
  --nx-fill-color-extra-light: #fafcff;
  --nx-fill-color-blank: #ffffff;

  // 阴影
  --nx-box-shadow: 0 12px 32px 4px rgba(0, 0, 0, 0.04), 0 8px 20px rgba(0, 0, 0, 0.08);
  --nx-box-shadow-light: 0 0 12px rgba(0, 0, 0, 0.12);
  --nx-box-shadow-lighter: 0 0 6px rgba(0, 0, 0, 0.12);
  --nx-box-shadow-dark: 0 16px 48px 16px rgba(0, 0, 0, 0.08), 0 12px 32px rgba(0, 0, 0, 0.12), 0 8px 16px -8px rgba(0, 0, 0, 0.16);

  // 字体
  --nx-font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  --nx-font-family-mono: 'DejaVu Sans Mono', Monaco, Consolas, 'Courier New', monospace;

  // 字体大小
  --nx-font-size-extra-large: 20px;
  --nx-font-size-large: 18px;
  --nx-font-size-medium: 16px;
  --nx-font-size-base: 14px;
  --nx-font-size-small: 13px;
  --nx-font-size-extra-small: 12px;

  // 圆角
  --nx-border-radius-base: 4px;
  --nx-border-radius-small: 2px;
  --nx-border-radius-round: 20px;
  --nx-border-radius-circle: 100%;

  // 间距
  --nx-spacing-xs: 4px;
  --nx-spacing-sm: 8px;
  --nx-spacing-md: 16px;
  --nx-spacing-lg: 24px;
  --nx-spacing-xl: 32px;

  // 动画
  --nx-transition-duration: 0.3s;
  --nx-transition-function: cubic-bezier(0.645, 0.045, 0.355, 1);
}

// ============= 暗色主题 =============

[nx-theme="dark"] {
  // 基础颜色
  --nx-primary: #409eff;
  --nx-success: #67c23a;
  --nx-warning: #e6a23c;
  --nx-danger: #f56c6c;
  --nx-info: #909399;

  // 文本颜色
  --nx-text-primary: #e5eaf3;
  --nx-text-regular: #cfd3dc;
  --nx-text-secondary: #a3a6ad;
  --nx-text-placeholder: #8d9095;

  // 边框颜色
  --nx-border-darker: #636466;
  --nx-border-dark: #58585b;
  --nx-border-base: #4c4d4f;
  --nx-border-light: #414243;
  --nx-border-lighter: #363637;
  --nx-border-extra-light: #2b2b2c;

  // 背景颜色
  --nx-bg-color: #141414;
  --nx-bg-color-page: #0a0a0a;
  --nx-bg-color-overlay: #1d1e1f;

  // 填充颜色
  --nx-fill-color-darker: #48494b;
  --nx-fill-color-dark: #3d3e40;
  --nx-fill-color: #303133;
  --nx-fill-color-light: #262727;
  --nx-fill-color-lighter: #1d1d1d;
  --nx-fill-color-extra-light: #191919;
  --nx-fill-color-blank: #141414;
}

// ============= 粉色主题 =============

[nx-theme="pink"] {
  // 基础颜色
  --nx-primary: #ec4899;
  --nx-success: #10b981;
  --nx-warning: #f59e0b;
  --nx-danger: #ef4444;
  --nx-info: #6b7280;

  // 文本颜色
  --nx-text-primary: #831843;
  --nx-text-regular: #be185d;
  --nx-text-secondary: #ec4899;
  --nx-text-placeholder: #f9a8d4;

  // 边框颜色
  --nx-border-darker: #be185d;
  --nx-border-dark: #db2777;
  --nx-border-base: #ec4899;
  --nx-border-light: #f472b6;
  --nx-border-lighter: #f9a8d4;
  --nx-border-extra-light: #fce7f3;

  // 背景颜色
  --nx-bg-color: #fdf2f8;
  --nx-bg-color-page: #fce7f3;
  --nx-bg-color-overlay: #ffffff;

  // 填充颜色
  --nx-fill-color-darker: #f9a8d4;
  --nx-fill-color-dark: #fbb6ce;
  --nx-fill-color: #fce7f3;
  --nx-fill-color-light: #fdf2f8;
  --nx-fill-color-lighter: #fefcfd;
  --nx-fill-color-extra-light: #ffffff;
  --nx-fill-color-blank: #ffffff;
}

// ============= 全局样式重置 =============

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html,
body {
  height: 100%;
  font-family: var(--nx-font-family);
  font-size: var(--nx-font-size-base);
  color: var(--nx-text-primary);
  background-color: var(--nx-bg-color);
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#app {
  height: 100%;
  width: 100%;
}

// ============= 滚动条样式 =============

::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--nx-fill-color-light);
  border-radius: var(--nx-border-radius-base);
}

::-webkit-scrollbar-thumb {
  background: var(--nx-fill-color-darker);
  border-radius: var(--nx-border-radius-base);
  
  &:hover {
    background: var(--nx-fill-color-dark);
  }
}

// ============= 选择文本样式 =============

::selection {
  background-color: var(--nx-primary);
  color: #ffffff;
}

::-moz-selection {
  background-color: var(--nx-primary);
  color: #ffffff;
}

// ============= 链接样式 =============

a {
  color: var(--nx-primary);
  text-decoration: none;
  transition: color var(--nx-transition-duration) var(--nx-transition-function);

  &:hover {
    color: var(--nx-primary);
    opacity: 0.8;
  }
}

// ============= 按钮基础样式 =============

button {
  font-family: inherit;
  font-size: inherit;
  cursor: pointer;
  border: none;
  outline: none;
  background: transparent;
  transition: all var(--nx-transition-duration) var(--nx-transition-function);

  &:disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }
}

// ============= 输入框基础样式 =============

input,
textarea {
  font-family: inherit;
  font-size: inherit;
  color: inherit;
  border: 1px solid var(--nx-border-base);
  border-radius: var(--nx-border-radius-base);
  padding: var(--nx-spacing-sm);
  background-color: var(--nx-bg-color);
  transition: border-color var(--nx-transition-duration) var(--nx-transition-function);

  &:focus {
    outline: none;
    border-color: var(--nx-primary);
  }

  &::placeholder {
    color: var(--nx-text-placeholder);
  }
}

// ============= 工具类 =============

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.hidden {
  display: none;
}

.invisible {
  visibility: hidden;
}

.pointer {
  cursor: pointer;
}

.no-select {
  user-select: none;
}

// ============= 响应式断点 =============

@media (max-width: 768px) {
  :root {
    --nx-font-size-base: 13px;
    --nx-spacing-md: 12px;
    --nx-spacing-lg: 16px;
    --nx-spacing-xl: 24px;
  }
}

@media (max-width: 480px) {
  :root {
    --nx-font-size-base: 12px;
    --nx-spacing-md: 8px;
    --nx-spacing-lg: 12px;
    --nx-spacing-xl: 16px;
  }
}
