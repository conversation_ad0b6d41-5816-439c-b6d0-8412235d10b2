/**
 * Vue Router 4 配置
 * 迁移自原有的 Vue Router 3 配置
 */

import { createRouter, createWebHistory, RouteRecordRaw } from 'vue-router'
import MainLayout from '@/layout/MainLayout.vue'

const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    name: 'Home',
    component: MainLayout,
    children: [
      {
        path: 'welcome/:id',
        name: 'Welcome',
        component: () => import('../views/Welcome.vue'),
        props: true
      },
      {
        path: 'shell/:sessionId',
        name: 'XTermSession',
        component: () => import('../views/xterm/XTermSession.vue'),
        props: true
      },
      {
        path: 'setting/:id',
        name: 'XTermProfile',
        component: () => import('../views/xterm/Profile.vue'),
        props: true
      },
      {
        path: 'login/:id',
        name: 'Login',
        component: () => import('../views/Login.vue'),
        props: true
      },
      {
        path: 'sftp/:sessionId',
        name: 'SFTP',
        component: () => import('../views/sftp/Index.vue'),
        props: true
      },
      {
        path: 'editor/:sessionId',
        name: 'EDITOR',
        component: () => import('../views/editor/Index.vue'),
        props: true
      },
      {
        path: 'vnc/:sessionId',
        name: 'VNC',
        component: () => import('../views/vnc/Index.vue'),
        props: true
      },
      {
        path: 'ftp/:sessionId',
        name: 'FTP',
        component: () => import('../views/sftp/Index.vue'),
        props: true
      },
      {
        path: 'webdav/:sessionId',
        name: 'WEBDAV',
        component: () => import('../views/sftp/Index.vue'),
        props: true
      },
      {
        path: 'globalsetting/:sessionId',
        name: 'GlobalSetting',
        component: () => import('../views/settings/Index.vue'),
        props: true
      }
    ]
  },
  {
    path: '/lock',
    name: 'Lock',
    component: () => import('../views/Lock.vue')
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    redirect: '/'
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 这里可以添加路由守卫逻辑
  console.log(`Navigating from ${from.path} to ${to.path}`)
  next()
})

router.afterEach((to, from) => {
  // 路由跳转后的处理
  console.log(`Navigation completed: ${from.path} -> ${to.path}`)
})

// 错误处理
router.onError((error) => {
  console.error('Router error:', error)
})

export default router
