{"extends": "@electron-toolkit/tsconfig/tsconfig.web.json", "include": ["src/renderer/src/env.d.ts", "src/renderer/src/**/*", "src/renderer/src/**/*.vue", "src/preload/*.d.ts", "src/shared/**/*"], "compilerOptions": {"composite": true, "baseUrl": ".", "target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "preserve", "strict": true, "noUnusedLocals": false, "noUnusedParameters": false, "noFallthroughCasesInSwitch": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "paths": {"@renderer/*": ["src/renderer/src/*"], "@/*": ["src/renderer/src/*"], "@shared/*": ["src/shared/*"], "@shared/types": ["src/shared/types/index.ts"]}, "types": ["node", "vite/client", "element-plus/global"]}}