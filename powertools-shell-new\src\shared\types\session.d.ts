/**
 * 会话相关类型定义
 * 定义所有会话管理相关的类型和接口
 */

// ============= 会话类型枚举 =============

export type SessionType = 
  | 'ssh' 
  | 'sftp' 
  | 'ftp' 
  | 'telnet' 
  | 'localshell' 
  | 'serialport' 
  | 'vnc' 
  | 'webdav'
  | 'rdp'
  | 'mosh'

export type SessionStatus = 
  | 'disconnected' 
  | 'connecting' 
  | 'connected' 
  | 'reconnecting' 
  | 'error' 
  | 'timeout'

export type AuthType = 
  | 'password' 
  | 'publickey' 
  | 'keyboard-interactive' 
  | 'none'

// ============= 会话配置接口 =============

export interface BaseSessionConfig {
  uuid: string
  name: string
  type: 'folder' | 'node'
  description?: string
  tags?: string[]
  createdAt?: Date
  updatedAt?: Date
  parentUuid?: string
  sortOrder?: number
}

export interface SessionNodeConfig extends BaseSessionConfig {
  type: 'node'
  sessType: SessionType
  config: SessionConnectionConfig
}

export interface SessionFolderConfig extends BaseSessionConfig {
  type: 'folder'
  subSessions?: SessionConfig[]
  expanded?: boolean
}

export type SessionConfig = SessionNodeConfig | SessionFolderConfig

// ============= 连接配置接口 =============

export interface BaseConnectionConfig {
  host?: string
  port?: number
  username?: string
  password?: string
  timeout?: number
  keepAlive?: boolean
  keepAliveInterval?: number
}

export interface SSHConnectionConfig extends BaseConnectionConfig {
  authType: AuthType
  privateKeyPath?: string
  privateKeyPassphrase?: string
  jumpHost?: string
  jumpPort?: number
  jumpUsername?: string
  jumpAuthType?: AuthType
  jumpPrivateKeyPath?: string
  compression?: boolean
  algorithms?: {
    kex?: string[]
    cipher?: string[]
    serverHostKey?: string[]
    hmac?: string[]
  }
  forwardAgent?: boolean
  x11Forwarding?: boolean
  localForwards?: Array<{
    localPort: number
    remoteHost: string
    remotePort: number
  }>
  remoteForwards?: Array<{
    remotePort: number
    localHost: string
    localPort: number
  }>
}

export interface SFTPConnectionConfig extends SSHConnectionConfig {
  remoteDirectory?: string
  localDirectory?: string
  transferMode?: 'binary' | 'ascii'
  preserveTimestamp?: boolean
}

export interface FTPConnectionConfig extends BaseConnectionConfig {
  passive?: boolean
  secure?: boolean
  secureOptions?: {
    rejectUnauthorized?: boolean
    checkServerIdentity?: boolean
  }
  encoding?: string
}

export interface TelnetConnectionConfig extends BaseConnectionConfig {
  localEcho?: boolean
  negotiationMandatory?: boolean
  initialLFToCRLF?: boolean
  echoLines?: number
  stripShellPrompt?: boolean
}

export interface LocalShellConfig {
  shell?: string
  args?: string[]
  cwd?: string
  env?: Record<string, string>
}

export interface SerialPortConfig {
  path: string
  baudRate: number
  dataBits?: 5 | 6 | 7 | 8
  stopBits?: 1 | 1.5 | 2
  parity?: 'none' | 'even' | 'mark' | 'odd' | 'space'
  rtscts?: boolean
  xon?: boolean
  xoff?: boolean
  xany?: boolean
}

export interface VNCConnectionConfig extends BaseConnectionConfig {
  quality?: number
  compression?: number
  viewOnly?: boolean
  shared?: boolean
  localCursor?: boolean
  dotCursor?: boolean
  scaling?: boolean
  clipboardUp?: boolean
  clipboardDown?: boolean
}

export interface WebDAVConnectionConfig extends BaseConnectionConfig {
  protocol?: 'http' | 'https'
  basePath?: string
  digest?: boolean
}

export type SessionConnectionConfig = 
  | SSHConnectionConfig
  | SFTPConnectionConfig
  | FTPConnectionConfig
  | TelnetConnectionConfig
  | LocalShellConfig
  | SerialPortConfig
  | VNCConnectionConfig
  | WebDAVConnectionConfig

// ============= 会话实例接口 =============

export interface SessionInstance {
  id: string
  uuid: string
  name: string
  type: SessionType
  status: SessionStatus
  config: SessionConnectionConfig
  createdAt: Date
  connectedAt?: Date
  lastActivity?: Date
  pid?: number
  channelId?: string
  metadata?: Record<string, any>
}

export interface SessionStatistics {
  totalSessions: number
  activeSessions: number
  sessionsByType: Record<SessionType, number>
  sessionsByStatus: Record<SessionStatus, number>
  totalConnectTime: number
  averageConnectTime: number
}

// ============= 会话事件接口 =============

export interface SessionEvent {
  type: 'created' | 'connected' | 'disconnected' | 'error' | 'data' | 'resize'
  sessionId: string
  timestamp: Date
  data?: any
  error?: Error
}

export interface SessionDataEvent extends SessionEvent {
  type: 'data'
  data: {
    content: string | Buffer
    encoding?: string
  }
}

export interface SessionResizeEvent extends SessionEvent {
  type: 'resize'
  data: {
    cols: number
    rows: number
  }
}

export interface SessionErrorEvent extends SessionEvent {
  type: 'error'
  error: Error
  data?: {
    code?: string
    level?: 'warning' | 'error' | 'fatal'
  }
}

// ============= 会话管理器接口 =============

export interface SessionManager {
  // 配置管理
  loadSessionConfigs(): Promise<SessionConfig[]>
  saveSessionConfigs(configs: SessionConfig[]): Promise<void>
  addSessionConfig(config: SessionConfig): Promise<void>
  updateSessionConfig(uuid: string, updates: Partial<SessionConfig>): Promise<void>
  removeSessionConfig(uuid: string): Promise<void>
  getSessionConfig(uuid: string): Promise<SessionConfig | null>
  
  // 实例管理
  createSessionInstance(uuid: string): Promise<SessionInstance>
  getSessionInstance(id: string): SessionInstance | null
  getSessionInstances(): SessionInstance[]
  getSessionInstanceByUUID(uuid: string): SessionInstance | null
  destroySessionInstance(id: string): Promise<void>
  
  // 连接管理
  connectSession(id: string): Promise<void>
  disconnectSession(id: string): Promise<void>
  reconnectSession(id: string): Promise<void>
  
  // 数据传输
  sendData(id: string, data: string | Buffer): Promise<void>
  resizeTerminal(id: string, cols: number, rows: number): Promise<void>
  
  // 事件监听
  on(event: string, listener: (...args: any[]) => void): void
  off(event: string, listener: (...args: any[]) => void): void
  emit(event: string, ...args: any[]): void
  
  // 统计信息
  getStatistics(): SessionStatistics
}

// ============= 导出所有类型 =============

export type {
  BaseSessionConfig,
  SessionNodeConfig,
  SessionFolderConfig,
  BaseConnectionConfig,
  SSHConnectionConfig,
  SFTPConnectionConfig,
  FTPConnectionConfig,
  TelnetConnectionConfig,
  LocalShellConfig,
  SerialPortConfig,
  VNCConnectionConfig,
  WebDAVConnectionConfig
}
