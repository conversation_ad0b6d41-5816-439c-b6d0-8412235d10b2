/**
 * IPC 处理器
 * 处理渲染进程发送的各种IPC请求
 */

import { ipcMain, app, BrowserWindow } from 'electron'
import { getCoreServices } from './core'

/**
 * 设置所有IPC处理器
 */
export function setupIPCHandlers(): void {
  console.log('Setting up IPC handlers...')

  // ============= 应用信息相关 =============

  ipcMain.handle('get-current-window', () => {
    return BrowserWindow.getFocusedWindow()
  })

  ipcMain.on('get-app-data-path', (event) => {
    event.returnValue = app.getPath('appData')
  })

  ipcMain.on('get-home-path', (event) => {
    event.returnValue = app.getPath('home')
  })

  ipcMain.on('get-logs-path', (event) => {
    event.returnValue = app.getPath('logs')
  })

  ipcMain.on('get-app-path', (event) => {
    event.returnValue = app.getAppPath()
  })

  ipcMain.on('get-app-version', (event) => {
    event.returnValue = app.getVersion()
  })

  ipcMain.on('get-portable', (event) => {
    // TODO: 实现便携版检测逻辑
    event.returnValue = false
  })

  ipcMain.on('get-web-link', (event) => {
    // TODO: 实现网站链接获取
    event.returnValue = 'https://github.com/nxshell/shell'
  })

  // ============= 屏幕录制相关 =============

  ipcMain.handle('capture-start', async () => {
    // TODO: 实现屏幕录制开始
    console.log('Screen capture start requested')
  })

  ipcMain.handle('capture-stop', async () => {
    // TODO: 实现屏幕录制停止
    console.log('Screen capture stop requested')
    return null
  })

  // ============= 高速IPC相关 =============

  ipcMain.handle('create-hsipc', async (_, { unixFile }) => {
    const { channelManager } = getCoreServices()
    return await channelManager.createHsIPCChannel(unixFile)
  })

  ipcMain.handle('hsipc-send', async (_, { channelId, data }) => {
    const { channelManager } = getCoreServices()
    return await channelManager.sendHsIPCData(channelId, data)
  })

  ipcMain.on('hsipc-close', (_, { channelId }) => {
    const { channelManager } = getCoreServices()
    channelManager.closeHsIPCChannel(channelId)
  })

  // ============= RPC相关 =============

  setupRPCHandlers()

  // ============= 通道相关 =============

  setupChannelHandlers()

  console.log('IPC handlers setup completed')
}

/**
 * 设置RPC处理器
 */
function setupRPCHandlers(): void {
  ipcMain.on('rpc-request', async (event, serviceName, request) => {
    const { rpcServer } = getCoreServices()

    try {
      const result = await rpcServer.handleRequest(serviceName, request)
      event.reply(`rpc-response:${serviceName}`, {
        id: request.id,
        result
      })
    } catch (error) {
      event.reply(`rpc-response:${serviceName}`, {
        id: request.id,
        error: {
          code: -1,
          message: error instanceof Error ? error.message : String(error)
        }
      })
    }
  })

  // 处理不同服务的RPC请求
  const services = ['', 'session', 'storage', 'filesystem', 'system', 'channel']

  services.forEach(serviceName => {
    ipcMain.on(`rpc-request:${serviceName}`, async (event, request) => {
      const { rpcServer } = getCoreServices()

      try {
        const result = await rpcServer.handleRequest(serviceName, request)
        event.reply(`rpc-response:${serviceName}`, {
          id: request.id,
          result
        })
      } catch (error) {
        event.reply(`rpc-response:${serviceName}`, {
          id: request.id,
          error: {
            code: -1,
            message: error instanceof Error ? error.message : String(error)
          }
        })
      }
    })
  })
}

/**
 * 设置通道处理器
 */
function setupChannelHandlers(): void {
  const services = ['', 'session', 'storage', 'filesystem', 'system', 'channel']

  services.forEach(serviceName => {
    // 创建通道
    ipcMain.on(`channel-create:${serviceName}`, (event, { channelId }) => {
      const { channelManager } = getCoreServices()
      channelManager.createChannel(channelId, event.sender)
    })

    // 发送通道数据
    ipcMain.on(`channel-send:${serviceName}`, (_, { channelId, data }) => {
      const { channelManager } = getCoreServices()
      channelManager.sendChannelData(channelId, data)
    })

    // 关闭通道
    ipcMain.on(`channel-close:${serviceName}`, (_, { channelId }) => {
      const { channelManager } = getCoreServices()
      channelManager.closeChannel(channelId)
    })
  })
}

/**
 * 清理IPC处理器
 */
export function cleanupIPCHandlers(): void {
  console.log('Cleaning up IPC handlers...')

  // 移除所有监听器
  ipcMain.removeAllListeners()

  console.log('IPC handlers cleaned up')
}
