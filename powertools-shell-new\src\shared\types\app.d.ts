/**
 * 应用相关类型定义
 * 定义应用配置、设置、插件等相关类型
 */

// ============= 应用基础类型 =============

export interface AppInfo {
  name: string
  version: string
  description: string
  author: string
  homepage: string
  repository: string
  license: string
  buildTime: Date
  commitHash?: string
  isPortable: boolean
  platform: string
  arch: string
}

export interface AppPaths {
  app: string
  userData: string
  temp: string
  logs: string
  config: string
  cache: string
  downloads: string
  documents: string
  desktop: string
  home: string
}

// ============= 应用配置类型 =============

export interface AppConfig {
  // 基础设置
  language: string
  theme: 'light' | 'dark' | 'auto'
  autoUpdate: boolean
  checkUpdateOnStartup: boolean
  
  // 窗口设置
  window: {
    width: number
    height: number
    x?: number
    y?: number
    maximized: boolean
    fullscreen: boolean
    alwaysOnTop: boolean
    frame: boolean
    transparent: boolean
    opacity: number
  }
  
  // 安全设置
  security: {
    rememberPasswords: boolean
    encryptPasswords: boolean
    autoLock: boolean
    lockTimeout: number
    requirePasswordOnWake: boolean
  }
  
  // 性能设置
  performance: {
    hardwareAcceleration: boolean
    maxMemoryUsage: number
    enableLogging: boolean
    logLevel: 'debug' | 'info' | 'warn' | 'error'
    maxLogFiles: number
  }
  
  // 网络设置
  network: {
    proxy?: {
      type: 'http' | 'https' | 'socks4' | 'socks5'
      host: string
      port: number
      username?: string
      password?: string
    }
    timeout: number
    retries: number
    keepAlive: boolean
  }
  
  // 备份设置
  backup: {
    enabled: boolean
    interval: number
    maxBackups: number
    includePasswords: boolean
    backupPath: string
  }
}

// ============= 插件系统类型 =============

export interface PluginManifest {
  id: string
  name: string
  version: string
  description: string
  author: string
  homepage?: string
  repository?: string
  license: string
  keywords: string[]
  
  // 依赖
  engines: {
    powertools: string
    node?: string
  }
  dependencies?: Record<string, string>
  peerDependencies?: Record<string, string>
  
  // 入口点
  main: string
  renderer?: string
  preload?: string
  
  // 权限
  permissions: string[]
  
  // 配置
  contributes?: {
    commands?: PluginCommand[]
    menus?: PluginMenu[]
    keybindings?: PluginKeybinding[]
    themes?: PluginTheme[]
    languages?: PluginLanguage[]
    settings?: PluginSetting[]
  }
}

export interface PluginCommand {
  command: string
  title: string
  category?: string
  icon?: string
  when?: string
}

export interface PluginMenu {
  command: string
  when?: string
  group?: string
  order?: number
}

export interface PluginKeybinding {
  command: string
  key: string
  when?: string
  mac?: string
  linux?: string
  win?: string
}

export interface PluginTheme {
  id: string
  label: string
  path: string
  uiTheme: 'light' | 'dark'
}

export interface PluginLanguage {
  id: string
  aliases: string[]
  extensions: string[]
  configuration?: string
}

export interface PluginSetting {
  key: string
  type: 'string' | 'number' | 'boolean' | 'array' | 'object'
  default: any
  title: string
  description: string
  enum?: any[]
  minimum?: number
  maximum?: number
}

export interface Plugin {
  manifest: PluginManifest
  path: string
  enabled: boolean
  loaded: boolean
  instance?: any
  error?: Error
  loadTime?: Date
}

export interface PluginManager {
  // 插件生命周期
  loadPlugin(path: string): Promise<Plugin>
  unloadPlugin(id: string): Promise<void>
  enablePlugin(id: string): Promise<void>
  disablePlugin(id: string): Promise<void>
  reloadPlugin(id: string): Promise<void>
  
  // 插件查询
  getPlugin(id: string): Plugin | null
  getPlugins(): Plugin[]
  getEnabledPlugins(): Plugin[]
  getLoadedPlugins(): Plugin[]
  
  // 插件安装
  installPlugin(source: string): Promise<Plugin>
  uninstallPlugin(id: string): Promise<void>
  updatePlugin(id: string): Promise<Plugin>
  
  // 插件市场
  searchPlugins(query: string): Promise<PluginSearchResult[]>
  getPluginInfo(id: string): Promise<PluginInfo>
  
  // 事件
  on(event: string, listener: (...args: any[]) => void): void
  off(event: string, listener: (...args: any[]) => void): void
}

export interface PluginSearchResult {
  id: string
  name: string
  description: string
  version: string
  author: string
  downloads: number
  rating: number
  tags: string[]
  lastUpdated: Date
}

export interface PluginInfo extends PluginSearchResult {
  readme: string
  changelog: string
  screenshots: string[]
  dependencies: Record<string, string>
  size: number
}

// ============= 更新系统类型 =============

export interface UpdateInfo {
  version: string
  releaseDate: Date
  releaseNotes: string
  downloadUrl: string
  size: number
  signature?: string
  checksum?: string
  mandatory: boolean
}

export interface UpdateProgress {
  percent: number
  transferred: number
  total: number
  speed: number
  estimatedTime: number
}

export interface UpdateManager {
  // 检查更新
  checkForUpdates(): Promise<UpdateInfo | null>
  downloadUpdate(info: UpdateInfo): Promise<void>
  installUpdate(): Promise<void>
  
  // 自动更新
  setAutoUpdate(enabled: boolean): void
  isAutoUpdateEnabled(): boolean
  
  // 进度监控
  onUpdateAvailable(listener: (info: UpdateInfo) => void): void
  onUpdateDownloaded(listener: () => void): void
  onDownloadProgress(listener: (progress: UpdateProgress) => void): void
  onError(listener: (error: Error) => void): void
}

// ============= 日志系统类型 =============

export type LogLevel = 'debug' | 'info' | 'warn' | 'error' | 'fatal'

export interface LogEntry {
  timestamp: Date
  level: LogLevel
  category: string
  message: string
  data?: any
  stack?: string
}

export interface Logger {
  debug(message: string, data?: any): void
  info(message: string, data?: any): void
  warn(message: string, data?: any): void
  error(message: string, error?: Error, data?: any): void
  fatal(message: string, error?: Error, data?: any): void
  
  // 子日志器
  child(category: string): Logger
  
  // 配置
  setLevel(level: LogLevel): void
  getLevel(): LogLevel
}

export interface LogManager {
  // 日志器管理
  getLogger(category?: string): Logger
  createLogger(category: string): Logger
  
  // 日志查询
  getLogs(options?: LogQueryOptions): Promise<LogEntry[]>
  clearLogs(): Promise<void>
  exportLogs(path: string, options?: LogExportOptions): Promise<void>
  
  // 配置
  setGlobalLevel(level: LogLevel): void
  setMaxLogFiles(count: number): void
  setLogRotation(enabled: boolean): void
}

export interface LogQueryOptions {
  level?: LogLevel
  category?: string
  startTime?: Date
  endTime?: Date
  limit?: number
  offset?: number
}

export interface LogExportOptions {
  format: 'json' | 'csv' | 'txt'
  includeData: boolean
  compress: boolean
}

// ============= 应用管理器接口 =============

export interface ApplicationManager {
  // 应用信息
  getAppInfo(): AppInfo
  getAppPaths(): AppPaths
  getAppConfig(): AppConfig
  setAppConfig(config: Partial<AppConfig>): Promise<void>
  
  // 生命周期
  restart(): Promise<void>
  quit(): Promise<void>
  
  // 插件管理
  getPluginManager(): PluginManager
  
  // 更新管理
  getUpdateManager(): UpdateManager
  
  // 日志管理
  getLogManager(): LogManager
  
  // 事件
  on(event: string, listener: (...args: any[]) => void): void
  off(event: string, listener: (...args: any[]) => void): void
}

// ============= 导出类型 =============

export type {
  AppInfo,
  AppPaths,
  AppConfig,
  PluginManifest,
  PluginCommand,
  PluginMenu,
  PluginKeybinding,
  PluginTheme,
  PluginLanguage,
  PluginSetting,
  Plugin,
  PluginManager,
  PluginSearchResult,
  PluginInfo,
  UpdateInfo,
  UpdateProgress,
  UpdateManager,
  LogLevel,
  LogEntry,
  Logger,
  LogManager,
  LogQueryOptions,
  LogExportOptions,
  ApplicationManager
}
