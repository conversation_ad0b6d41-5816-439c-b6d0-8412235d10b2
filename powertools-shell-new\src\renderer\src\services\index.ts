/**
 * 服务设置
 * 为Vue 3应用设置各种服务
 */

import type { App } from 'vue'

export function setupServices(app: App) {
  // 设置全局属性
  app.config.globalProperties.$powertools = window.powertools
  
  // 设置错误处理
  app.config.errorHandler = (err, instance, info) => {
    console.error('Vue error:', err)
    console.error('Component instance:', instance)
    console.error('Error info:', info)
  }
  
  // 设置警告处理
  app.config.warnHandler = (msg, instance, trace) => {
    console.warn('Vue warning:', msg)
    console.warn('Component instance:', instance)
    console.warn('Component trace:', trace)
  }
  
  console.log('Services setup completed')
}
