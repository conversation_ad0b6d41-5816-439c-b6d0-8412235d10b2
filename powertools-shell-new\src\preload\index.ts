import { contextBridge, ipc<PERSON><PERSON><PERSON>, clipboard, shell } from 'electron'
import { electronAPI } from '@electron-toolkit/preload'
import type { PowerToolsAPI, PowerToolsService, HsIPCChannel } from '../shared/types/powertools'

// ============= RPC 客户端实现 =============

class RPCClient {
  private requestId = 0
  private pendingRequests = new Map<string, { resolve: Function; reject: Function }>()

  constructor(private serviceName: string) {
    // 监听RPC响应
    ipcRenderer.on(`rpc-response:${serviceName}`, (_: any, response: any) => {
      const pending = this.pendingRequests.get(response.id)
      if (pending) {
        this.pendingRequests.delete(response.id)
        if (response.error) {
          pending.reject(new Error(response.error.message))
        } else {
          pending.resolve(response.result)
        }
      }
    })
  }

  async call(method: string, ...params: any[]): Promise<any> {
    const id = `${this.serviceName}-${++this.requestId}`

    return new Promise((resolve, reject) => {
      this.pendingRequests.set(id, { resolve, reject })

      ipcRenderer.send(`rpc-request:${this.serviceName}`, {
        id,
        method,
        params
      })

      // 设置超时
      setTimeout(() => {
        if (this.pendingRequests.has(id)) {
          this.pendingRequests.delete(id)
          reject(new Error(`RPC call timeout: ${method}`))
        }
      }, 30000)
    })
  }
}

// ============= 通道客户端实现 =============

class ChannelClient {
  private channels = new Map<string, any>()

  constructor(private serviceName: string) {
    // 监听通道数据
    ipcRenderer.on(`channel-data:${serviceName}`, (_: any, data: any) => {
      const channel = this.channels.get(data.channelId)
      if (channel && channel.onData) {
        channel.onData(data.data)
      }
    })
  }

  createChannel(): any {
    const channelId = `channel-${Date.now()}-${Math.random()}`
    const serviceName = this.serviceName
    const channels = this.channels

    const channel = {
      channelId,
      onData: null as ((data: any) => void) | null,

      on(event: string, callback: (data: any) => void) {
        if (event === 'data') {
          this.onData = callback
        }
      },

      send(data: any) {
        ipcRenderer.send(`channel-send:${serviceName}`, {
          channelId,
          data
        })
      },

      close() {
        channels.delete(channelId)
        ipcRenderer.send(`channel-close:${serviceName}`, { channelId })
      }
    }

    this.channels.set(channelId, channel)

    // 通知主进程创建通道
    ipcRenderer.send(`channel-create:${this.serviceName}`, { channelId })

    return channel
  }
}

// ============= PowerTools 服务实现 =============

class PowerToolsServiceImpl implements PowerToolsService {
  private rpcClient: RPCClient
  private channelClient: ChannelClient

  constructor(serviceName: string = '') {
    this.rpcClient = new RPCClient(serviceName)
    this.channelClient = new ChannelClient(serviceName)
  }

  createChannel() {
    return this.channelClient.createChannel()
  }

  async createNodeSessionInstance(uuid: string, config: any): Promise<number> {
    return this.rpcClient.call('createNodeSessionInstance', uuid, config)
  }

  async getNodeSessionInstanceByUUID(uuid: string): Promise<any> {
    return this.rpcClient.call('getNodeSessionInstanceByUUID', uuid)
  }

  createDataTransfer() {
    return this.rpcClient.call('createDataTransfer')
  }

  createFileStorage() {
    return this.rpcClient.call('createFileStorage')
  }

  async getSerialPorts(): Promise<Array<{ path: string }>> {
    return this.rpcClient.call('getSerialPorts')
  }

  createLogger() {
    return this.rpcClient.call('createLogger')
  }

  async getSystemFonts(): Promise<string[]> {
    return this.rpcClient.call('getSystemFonts')
  }

  async getHsIPCHandle(): Promise<string> {
    return this.rpcClient.call('getHsIPCHandle')
  }
}

// ============= 高速IPC通道实现 =============

class HsIPCChannelImpl implements HsIPCChannel {
  constructor(public channelId: string, private unixFile: string) {}

  async send(data: any): Promise<void> {
    return ipcRenderer.invoke('hsipc-send', {
      channelId: this.channelId,
      data
    })
  }

  on(_event: 'data', callback: (data: any) => void): void {
    ipcRenderer.on(`hsipc-data:${this.channelId}`, (_: any, data: any) => {
      callback(data)
    })
  }

  close(): void {
    ipcRenderer.send('hsipc-close', { channelId: this.channelId })
  }
}

// ============= PowerTools API 主实现 =============

const services = new Map<string, PowerToolsServiceImpl>()

const powertoolsAPI: PowerToolsAPI = {
  // 服务管理
  getService(serviceName: string = ''): PowerToolsService {
    if (!services.has(serviceName)) {
      services.set(serviceName, new PowerToolsServiceImpl(serviceName))
    }
    return services.get(serviceName)!
  },

  // 窗口管理
  getCurrentWindow() {
    return ipcRenderer.invoke('get-current-window')
  },

  // 剪贴板操作
  clipboardReadText(): string {
    return clipboard.readText()
  },

  clipboardWriteText(text: string): void {
    clipboard.writeText(text)
  },

  // 外部链接
  async openExterUrl(url: string): Promise<void> {
    await shell.openExternal(url)
  },

  openDialog(url: string, options?: Record<string, any>): Window | null {
    // 在渲染进程中打开新窗口
    const optionsString = options
      ? Object.keys(options).map(key => `${key}=${options[key]}`).join(',')
      : ''
    return window.open(url, 'modal', optionsString)
  },

  // 路径操作
  getAppDataDirty(): string {
    return ipcRenderer.sendSync('get-app-data-path')
  },

  getAppHomeDirty(): string {
    return ipcRenderer.sendSync('get-home-path')
  },

  getLogDirty(): string {
    return ipcRenderer.sendSync('get-logs-path')
  },

  getAppPath(): string {
    return ipcRenderer.sendSync('get-app-path')
  },

  async openPath(path: string): Promise<string> {
    return shell.openPath(path)
  },

  showItemInFolder(path: string): void {
    shell.showItemInFolder(path)
  },

  // 应用信息
  getVersion(): string {
    return ipcRenderer.sendSync('get-app-version')
  },

  getPortable(): boolean {
    return ipcRenderer.sendSync('get-portable')
  },

  getWebLink(): string {
    return ipcRenderer.sendSync('get-web-link')
  },

  getostype(): string {
    return process.platform
  },

  // 高速IPC
  async createHsIPC(unixFile: string): Promise<HsIPCChannel> {
    const channelId = await ipcRenderer.invoke('create-hsipc', { unixFile })
    return new HsIPCChannelImpl(channelId, unixFile)
  },

  // 屏幕录制
  async captureStart(): Promise<void> {
    return ipcRenderer.invoke('capture-start')
  },

  async captureStop(): Promise<Buffer | null> {
    return ipcRenderer.invoke('capture-stop')
  }
}

// ============= 暴露API到渲染进程 =============

// 检查是否启用了上下文隔离
const isContextIsolated = (process as any).contextIsolated !== false

if (isContextIsolated) {
  try {
    contextBridge.exposeInMainWorld('electron', electronAPI)
    contextBridge.exposeInMainWorld('powertools', powertoolsAPI)
  } catch (error) {
    console.error('Failed to expose APIs:', error)
  }
} else {
  // @ts-ignore
  window.electron = electronAPI
  // @ts-ignore
  window.powertools = powertoolsAPI
}
