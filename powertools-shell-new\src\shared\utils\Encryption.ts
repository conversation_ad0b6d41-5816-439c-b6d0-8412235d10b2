/**
 * 加密工具
 * 重构自原有的 encrypt.js，提供类型安全的加密解密功能
 */

import * as crypto from 'crypto'

/**
 * 加密结果接口
 */
export interface EncryptionResult {
  iv: string
  content: string
  algorithm?: string
}

/**
 * 加密配置接口
 */
export interface EncryptionConfig {
  algorithm: string
  secretKey: string
  ivLength: number
}

/**
 * 加密工具类
 */
export class Encryption {
  private config: EncryptionConfig

  constructor(config?: Partial<EncryptionConfig>) {
    this.config = {
      algorithm: 'aes-256-ctr',
      secretKey: 'jnNr2lXHpoTBhwog0FO01QvuKDpYl2T/',
      ivLength: 16,
      ...config
    }
  }

  /**
   * 加密文本
   * @param text 待加密的文本
   * @returns 加密结果
   */
  encrypt(text: string): EncryptionResult {
    try {
      const iv = crypto.randomBytes(this.config.ivLength)
      const cipher = crypto.createCipheriv(this.config.algorithm, this.config.secretKey, iv)

      const encrypted = Buffer.concat([cipher.update(text, 'utf8'), cipher.final()])

      return {
        iv: iv.toString('hex'),
        content: encrypted.toString('hex'),
        algorithm: this.config.algorithm
      }
    } catch (error) {
      throw new Error(`Encryption failed: ${error instanceof Error ? error.message : String(error)}`)
    }
  }

  /**
   * 解密文本
   * @param encryptionResult 加密结果
   * @returns 解密后的文本
   */
  decrypt(encryptionResult: EncryptionResult): string {
    try {
      const iv = Buffer.from(encryptionResult.iv, 'hex')
      const decipher = crypto.createDecipheriv(this.config.algorithm, this.config.secretKey, iv)

      const decrypted = Buffer.concat([
        decipher.update(Buffer.from(encryptionResult.content, 'hex')),
        decipher.final()
      ])

      return decrypted.toString('utf8')
    } catch (error) {
      throw new Error(`Decryption failed: ${error instanceof Error ? error.message : String(error)}`)
    }
  }

  /**
   * 使用指定IV加密（更安全的方式）
   * @param text 待加密的文本
   * @param iv 初始化向量
   * @returns 加密结果
   */
  encryptWithIV(text: string, iv?: Buffer): EncryptionResult {
    try {
      const initVector = iv || crypto.randomBytes(this.config.ivLength)
      const cipher = crypto.createCipheriv(this.config.algorithm, this.config.secretKey, initVector)

      const encrypted = Buffer.concat([cipher.update(text, 'utf8'), cipher.final()])

      return {
        iv: initVector.toString('hex'),
        content: encrypted.toString('hex'),
        algorithm: this.config.algorithm
      }
    } catch (error) {
      throw new Error(`Encryption with IV failed: ${error instanceof Error ? error.message : String(error)}`)
    }
  }

  /**
   * 使用指定IV解密
   * @param encryptionResult 加密结果
   * @returns 解密后的文本
   */
  decryptWithIV(encryptionResult: EncryptionResult): string {
    try {
      const iv = Buffer.from(encryptionResult.iv, 'hex')
      const decipher = crypto.createDecipheriv(this.config.algorithm, this.config.secretKey, iv)

      const decrypted = Buffer.concat([
        decipher.update(Buffer.from(encryptionResult.content, 'hex')),
        decipher.final()
      ])

      return decrypted.toString('utf8')
    } catch (error) {
      throw new Error(`Decryption with IV failed: ${error instanceof Error ? error.message : String(error)}`)
    }
  }

  /**
   * 生成随机密钥
   * @param length 密钥长度
   * @returns 密钥字符串
   */
  static generateKey(length: number = 32): string {
    return crypto.randomBytes(length).toString('hex')
  }

  /**
   * 生成随机IV
   * @param length IV长度
   * @returns IV Buffer
   */
  static generateIV(length: number = 16): Buffer {
    return crypto.randomBytes(length)
  }

  /**
   * 计算文本的哈希值
   * @param text 文本
   * @param algorithm 哈希算法
   * @returns 哈希值
   */
  static hash(text: string, algorithm: string = 'sha256'): string {
    return crypto.createHash(algorithm).update(text).digest('hex')
  }

  /**
   * 验证文本的哈希值
   * @param text 原文本
   * @param hash 哈希值
   * @param algorithm 哈希算法
   * @returns 是否匹配
   */
  static verifyHash(text: string, hash: string, algorithm: string = 'sha256'): boolean {
    const computedHash = this.hash(text, algorithm)
    return computedHash === hash
  }
}

/**
 * 密码加密工具类
 * 专门用于密码的安全存储
 */
export class PasswordEncryption {
  private static readonly SALT_LENGTH = 32
  private static readonly KEY_LENGTH = 64
  private static readonly ITERATIONS = 100000

  /**
   * 加密密码
   * @param password 明文密码
   * @param salt 盐值（可选）
   * @returns 加密结果
   */
  static async encryptPassword(password: string, salt?: string): Promise<{
    hash: string
    salt: string
  }> {
    try {
      const saltBuffer = salt ? Buffer.from(salt, 'hex') : crypto.randomBytes(this.SALT_LENGTH)

      return new Promise((resolve, reject) => {
        crypto.pbkdf2(password, saltBuffer, this.ITERATIONS, this.KEY_LENGTH, 'sha512', (err, derivedKey) => {
          if (err) {
            reject(new Error(`Password encryption failed: ${err.message}`))
          } else {
            resolve({
              hash: derivedKey.toString('hex'),
              salt: saltBuffer.toString('hex')
            })
          }
        })
      })
    } catch (error) {
      throw new Error(`Password encryption failed: ${error instanceof Error ? error.message : String(error)}`)
    }
  }

  /**
   * 验证密码
   * @param password 明文密码
   * @param hash 存储的哈希值
   * @param salt 盐值
   * @returns 是否匹配
   */
  static async verifyPassword(password: string, hash: string, salt: string): Promise<boolean> {
    try {
      const result = await this.encryptPassword(password, salt)
      return result.hash === hash
    } catch (error) {
      console.error('Password verification failed:', error)
      return false
    }
  }
}

/**
 * 默认加密实例
 */
const defaultEncryption = new Encryption()

/**
 * 快速加密函数（向后兼容）
 * @param text 待加密的文本
 * @returns 加密结果
 */
export function encrypt(text: string): EncryptionResult {
  return defaultEncryption.encrypt(text)
}

/**
 * 快速解密函数（向后兼容）
 * @param encryptionResult 加密结果
 * @returns 解密后的文本
 */
export function decrypt(encryptionResult: EncryptionResult): string {
  return defaultEncryption.decrypt(encryptionResult)
}

/**
 * 安全加密函数
 * @param text 待加密的文本
 * @returns 加密结果
 */
export function encryptSecure(text: string): EncryptionResult {
  return defaultEncryption.encryptWithIV(text)
}

/**
 * 安全解密函数
 * @param encryptionResult 加密结果
 * @returns 解密后的文本
 */
export function decryptSecure(encryptionResult: EncryptionResult): string {
  return defaultEncryption.decryptWithIV(encryptionResult)
}

// 导出默认实例
export default {
  Encryption,
  PasswordEncryption,
  encrypt,
  decrypt,
  encryptSecure,
  decryptSecure
}
