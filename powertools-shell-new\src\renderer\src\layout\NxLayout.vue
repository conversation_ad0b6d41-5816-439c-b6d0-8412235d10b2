<template>
  <div class="nx-layout">
    <div class="nx-layout-header">
      <div class="nx-layout-title">PowerTools Shell</div>
      <div class="nx-layout-controls">
        <!-- 窗口控制按钮 -->
        <button class="control-btn minimize" @click="minimizeWindow">
          <span>−</span>
        </button>
        <button class="control-btn maximize" @click="maximizeWindow">
          <span>□</span>
        </button>
        <button class="control-btn close" @click="closeWindow">
          <span>×</span>
        </button>
      </div>
    </div>
    
    <div class="nx-layout-body">
      <slot name="main-panel">
        <div class="nx-layout-placeholder">
          <h2>PowerTools Shell</h2>
          <p>Modern Terminal and SSH Client</p>
        </div>
      </slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'

// 窗口控制方法
const minimizeWindow = () => {
  if (typeof window.electron !== 'undefined') {
    window.electron.ipcRenderer.send('window-minimize')
  }
}

const maximizeWindow = () => {
  if (typeof window.electron !== 'undefined') {
    window.electron.ipcRenderer.send('window-maximize')
  }
}

const closeWindow = () => {
  if (typeof window.electron !== 'undefined') {
    window.electron.ipcRenderer.send('window-close')
  }
}

onMounted(() => {
  console.log('NxLayout mounted')
})
</script>

<style scoped lang="scss">
.nx-layout {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--nx-bg-color, #ffffff);
  color: var(--nx-text-color, #333333);
}

.nx-layout-header {
  height: 32px;
  background: var(--nx-header-bg, #f5f5f5);
  border-bottom: 1px solid var(--nx-border-color, #e0e0e0);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  -webkit-app-region: drag;
  user-select: none;
}

.nx-layout-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--nx-title-color, #333333);
}

.nx-layout-controls {
  display: flex;
  gap: 8px;
  -webkit-app-region: no-drag;
}

.control-btn {
  width: 24px;
  height: 24px;
  border: none;
  border-radius: 4px;
  background: transparent;
  color: var(--nx-control-color, #666666);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  line-height: 1;
  transition: all 0.2s ease;

  &:hover {
    background: var(--nx-control-hover-bg, #e0e0e0);
  }

  &.close:hover {
    background: #ff5f57;
    color: white;
  }

  span {
    display: block;
    transform: translateY(-1px);
  }
}

.nx-layout-body {
  flex: 1;
  overflow: hidden;
  position: relative;
}

.nx-layout-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;

  h2 {
    margin: 0 0 16px 0;
    font-size: 24px;
    font-weight: 600;
    color: var(--nx-primary-color, #409eff);
  }

  p {
    margin: 0;
    font-size: 16px;
    color: var(--nx-text-secondary, #666666);
  }
}

// 主题样式
:global([nx-theme="dark"]) {
  .nx-layout {
    --nx-bg-color: #1e1e1e;
    --nx-text-color: #ffffff;
    --nx-header-bg: #2d2d2d;
    --nx-border-color: #404040;
    --nx-title-color: #ffffff;
    --nx-control-color: #cccccc;
    --nx-control-hover-bg: #404040;
    --nx-text-secondary: #cccccc;
  }
}

:global([nx-theme="pink"]) {
  .nx-layout {
    --nx-bg-color: #fdf2f8;
    --nx-text-color: #831843;
    --nx-header-bg: #fce7f3;
    --nx-border-color: #f9a8d4;
    --nx-title-color: #831843;
    --nx-control-color: #be185d;
    --nx-control-hover-bg: #f3e8ff;
    --nx-primary-color: #ec4899;
    --nx-text-secondary: #be185d;
  }
}
</style>
