<template>
  <div class="welcome-container">
    <div class="welcome-header">
      <div class="logo">
        <div class="logo-placeholder">🚀</div>
      </div>
      <h1 class="title">PowerTools Shell</h1>
      <p class="subtitle">Modern Terminal and SSH Client</p>
      <p class="version">{{ version }}</p>
    </div>

    <div class="welcome-content">
      <div class="feature-grid">
        <div class="feature-card">
          <div class="feature-icon">🚀</div>
          <h3>High Performance</h3>
          <p>Built with modern technologies for optimal performance</p>
        </div>

        <div class="feature-card">
          <div class="feature-icon">🔒</div>
          <h3>Secure</h3>
          <p>Advanced security features to protect your connections</p>
        </div>

        <div class="feature-card">
          <div class="feature-icon">🎨</div>
          <h3>Customizable</h3>
          <p>Themes, fonts, and layouts to match your preferences</p>
        </div>

        <div class="feature-card">
          <div class="feature-icon">🌐</div>
          <h3>Multi-Protocol</h3>
          <p>SSH, SFTP, FTP, Telnet, VNC, and more protocols supported</p>
        </div>
      </div>

      <div class="action-buttons">
        <el-button type="primary" size="large" @click="createNewSession">
          Create New Session
        </el-button>

        <el-button size="large" @click="openSettings">
          Settings
        </el-button>
      </div>

      <div class="quick-links">
        <h3>Quick Links</h3>
        <div class="links">
          <a href="#" @click="toWebsite">
            <el-button link>
              <span>🌐</span>
              Official Website
            </el-button>
          </a>

          <a href="#" @click="toGitHub">
            <el-button link>
              <span>⭐</span>
              Star on GitHub
            </el-button>
          </a>

          <a href="#" @click="toIssues">
            <el-button link>
              <span>🐛</span>
              Report Issues
            </el-button>
          </a>

          <a href="#" @click="toDocumentation">
            <el-button link>
              <span>📚</span>
              Documentation
            </el-button>
          </a>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElButton } from 'element-plus'

const router = useRouter()

// 获取应用版本
const version = computed(() => {
  try {
    if (typeof window.powertools !== 'undefined') {
      return `V${window.powertools.getVersion()}`
    }
    return 'V2.0.0-dev'
  } catch {
    return 'V2.0.0-dev'
  }
})

// 创建新会话
const createNewSession = () => {
  // TODO: 实现创建新会话的逻辑
  console.log('Create new session clicked')
}

// 打开设置
const openSettings = () => {
  router.push({ name: 'GlobalSetting', params: { sessionId: 'settings' } })
}

// 外部链接处理
const toWebsite = () => {
  openExternalUrl('https://nxshell.github.io/')
}

const toGitHub = () => {
  openExternalUrl('https://github.com/nxshell/shell')
}

const toIssues = () => {
  openExternalUrl('https://github.com/nxshell/shell/issues')
}

const toDocumentation = () => {
  openExternalUrl('https://nxshell.github.io/docs/')
}

const openExternalUrl = (url: string) => {
  try {
    if (typeof window.powertools !== 'undefined') {
      window.powertools.openExterUrl(url)
    } else {
      window.open(url, '_blank')
    }
  } catch (error) {
    console.error('Failed to open external URL:', error)
    window.open(url, '_blank')
  }
}

onMounted(() => {
  console.log('Welcome page mounted')
})
</script>

<style scoped lang="scss">
.welcome-container {
  height: 100%;
  overflow-y: auto;
  padding: 40px;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: var(--nx-welcome-bg, linear-gradient(135deg, #667eea 0%, #764ba2 100%));
  color: var(--nx-welcome-text, #ffffff);
}

.welcome-header {
  text-align: center;
  margin-bottom: 40px;

  .logo {
    margin-bottom: 20px;

    .logo-placeholder {
      width: 80px;
      height: 80px;
      border-radius: 16px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
      background: rgba(255, 255, 255, 0.1);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 40px;
      margin: 0 auto;
    }
  }

  .title {
    font-size: 48px;
    font-weight: 700;
    margin: 0 0 8px 0;
    background: linear-gradient(45deg, #ffffff, #f0f0f0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .subtitle {
    font-size: 20px;
    margin: 0 0 8px 0;
    opacity: 0.9;
  }

  .version {
    font-size: 16px;
    margin: 0;
    opacity: 0.7;
    font-family: 'Monaco', 'Consolas', monospace;
  }
}

.welcome-content {
  max-width: 800px;
  width: 100%;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.feature-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 24px;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-4px);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
  }

  .feature-icon {
    font-size: 32px;
    margin-bottom: 12px;
  }

  h3 {
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 8px 0;
  }

  p {
    font-size: 14px;
    margin: 0;
    opacity: 0.8;
    line-height: 1.4;
  }
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-bottom: 40px;

  :deep(.el-button) {
    padding: 12px 24px;
    font-size: 16px;
    border-radius: 8px;
    font-weight: 500;
  }

  :deep(.el-button--primary) {
    background: linear-gradient(45deg, #409eff, #36a3f7);
    border: none;
    box-shadow: 0 4px 16px rgba(64, 158, 255, 0.3);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(64, 158, 255, 0.4);
    }
  }

  :deep(.el-button:not(.el-button--primary)) {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: #ffffff;

    &:hover {
      background: rgba(255, 255, 255, 0.2);
      transform: translateY(-2px);
    }
  }
}

.quick-links {
  text-align: center;

  h3 {
    font-size: 20px;
    font-weight: 600;
    margin: 0 0 20px 0;
    opacity: 0.9;
  }

  .links {
    display: flex;
    justify-content: center;
    gap: 24px;
    flex-wrap: wrap;

    a {
      text-decoration: none;
    }

    :deep(.el-button) {
      color: rgba(255, 255, 255, 0.8);
      font-size: 14px;
      padding: 8px 0;

      &:hover {
        color: #ffffff;
        transform: translateY(-1px);
      }

      span {
        margin-right: 8px;
      }
    }
  }
}

// 主题适配
:global([nx-theme="light"]) {
  .welcome-container {
    --nx-welcome-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --nx-welcome-text: #ffffff;
  }
}

:global([nx-theme="dark"]) {
  .welcome-container {
    --nx-welcome-bg: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    --nx-welcome-text: #ffffff;
  }
}

:global([nx-theme="pink"]) {
  .welcome-container {
    --nx-welcome-bg: linear-gradient(135deg, #ec4899 0%, #be185d 100%);
    --nx-welcome-text: #ffffff;
  }
}
</style>
