/**
 * 工具类统一导出
 * 提供所有工具类的统一入口
 */

// ============= ID生成器 =============
export {
  IdGenerator,
  UUIDGenerator,
  TimestampIdGenerator,
  SessionIdGenerator,
  ChannelIdGenerator,
  getGlobalId,
  resetGlobalId,
  getCurrentGlobalId
} from './IdGenerator'

// ============= 事件总线 =============
export {
  EventBus,
  TypedEventBus,
  initializeGlobalEventBus,
  getGlobalEventBus,
  destroyGlobalEventBus
} from './EventBus'

// ============= 加密工具 =============
export {
  Encryption,
  PasswordEncryption,
  encrypt,
  decrypt,
  encryptSecure,
  decryptSecure
} from './Encryption'

// ============= 通用工具 =============
export {
  isPromise,
  insert,
  resetValues,
  deepClone,
  deepMerge,
  isObject,
  debounce,
  throttle,
  delay,
  retry,
  formatBytes,
  formatDuration,
  randomString,
  safeJsonParse,
  safeJsonStringify,
  isEmpty
} from './CommonUtils'

// ============= 日志工具 =============
export {
  Logger,
  LogManager,
  ConsoleTransport,
  FileTransport,
  JsonFormatter,
  TextFormatter,
  debug,
  info,
  warn,
  error,
  fatal
} from './Logger'

// ============= 等待对象 =============
export {
  WaitObject,
  TimeoutWaitObject,
  CancellableWaitObject,
  WaitObjectFactory
} from './WaitObject'

// ============= 类型定义 =============
export type {
  EventListener,
  EventHandlerMap
} from './EventBus'

export type {
  EncryptionResult,
  EncryptionConfig
} from './Encryption'

export type {
  LoggerConfig,
  LogFormatter,
  LogTransport
} from './Logger'

// ============= 常量定义 =============

/**
 * 默认配置常量
 */
export const DEFAULT_CONFIG = {
  // ID生成器配置
  ID_GENERATOR: {
    INIT_ID: 0,
    MAX_SAFE_INTEGER: Number.MAX_SAFE_INTEGER
  },

  // 事件总线配置
  EVENT_BUS: {
    MAX_LISTENERS: 100,
    DEFAULT_TOPIC: 'default'
  },

  // 加密配置
  ENCRYPTION: {
    ALGORITHM: 'aes-256-ctr',
    IV_LENGTH: 16,
    KEY_LENGTH: 32
  },

  // 日志配置
  LOGGER: {
    DEFAULT_LEVEL: 'info' as const,
    MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
    MAX_FILES: 5,
    DATE_PATTERN: 'YYYY-MM-DD'
  },

  // 通用配置
  COMMON: {
    DEBOUNCE_DELAY: 300,
    THROTTLE_LIMIT: 1000,
    RETRY_COUNT: 3,
    RETRY_DELAY: 1000
  }
} as const

/**
 * 错误代码常量
 */
export const ERROR_CODES = {
  // ID生成器错误
  ID_GENERATOR_OVERFLOW: 'ID_GENERATOR_OVERFLOW',
  ID_GENERATOR_INVALID_INIT: 'ID_GENERATOR_INVALID_INIT',

  // 事件总线错误
  EVENT_BUS_INVALID_TOPIC: 'EVENT_BUS_INVALID_TOPIC',
  EVENT_BUS_INVALID_HANDLER: 'EVENT_BUS_INVALID_HANDLER',
  EVENT_BUS_MAX_LISTENERS: 'EVENT_BUS_MAX_LISTENERS',

  // 加密错误
  ENCRYPTION_FAILED: 'ENCRYPTION_FAILED',
  DECRYPTION_FAILED: 'DECRYPTION_FAILED',
  INVALID_ENCRYPTION_DATA: 'INVALID_ENCRYPTION_DATA',

  // 日志错误
  LOGGER_TRANSPORT_ERROR: 'LOGGER_TRANSPORT_ERROR',
  LOGGER_INVALID_LEVEL: 'LOGGER_INVALID_LEVEL',

  // 通用错误
  INVALID_PARAMETER: 'INVALID_PARAMETER',
  OPERATION_TIMEOUT: 'OPERATION_TIMEOUT',
  OPERATION_CANCELLED: 'OPERATION_CANCELLED'
} as const

/**
 * 工具函数集合
 */
export const Utils = {
  // ID生成
  generateId: () => getGlobalId(),
  generateUUID: () => UUIDGenerator.generate(),
  generateShortUUID: () => UUIDGenerator.generateShort(),

  // 事件处理
  createEventBus: () => new EventBus(),
  getEventBus: () => getGlobalEventBus(),

  // 加密解密
  encrypt: (text: string) => encrypt(text),
  decrypt: (data: any) => decrypt(data),
  encryptSecure: (text: string) => encryptSecure(text),
  decryptSecure: (data: any) => decryptSecure(data),

  // 日志记录
  createLogger: (category?: string) => LogManager.getInstance().getLogger(category),
  log: {
    debug: (message: string, data?: any) => debug(message, data),
    info: (message: string, data?: any) => info(message, data),
    warn: (message: string, data?: any) => warn(message, data),
    error: (message: string, error?: Error, data?: any) => error(message, error, data),
    fatal: (message: string, error?: Error, data?: any) => fatal(message, error, data)
  },

  // 通用工具
  clone: <T>(obj: T) => deepClone(obj),
  merge: <T extends Record<string, any>>(target: T, ...sources: Partial<T>[]) => deepMerge(target, ...sources),
  debounce: <T extends (...args: any[]) => any>(func: T, wait: number, immediate?: boolean) => debounce(func, wait, immediate),
  throttle: <T extends (...args: any[]) => any>(func: T, limit: number) => throttle(func, limit),
  delay: (ms: number) => delay(ms),
  retry: <T>(func: () => Promise<T>, maxRetries?: number, delayMs?: number) => retry(func, maxRetries, delayMs),
  formatBytes: (bytes: number, decimals?: number) => formatBytes(bytes, decimals),
  formatDuration: (ms: number) => formatDuration(ms),
  randomString: (length?: number, charset?: string) => randomString(length, charset),
  isEmpty: (obj: any) => isEmpty(obj),

  // JSON处理
  parseJSON: <T>(str: string, defaultValue: T) => safeJsonParse(str, defaultValue),
  stringifyJSON: (obj: any, defaultValue?: string) => safeJsonStringify(obj, defaultValue)
}

/**
 * 初始化工具类
 * @param config 配置选项
 */
export function initializeUtils(config?: {
  eventBus?: { maxListeners?: number }
  logger?: { level?: string; enableConsole?: boolean; enableFile?: boolean }
  encryption?: { algorithm?: string; secretKey?: string }
}): void {
  // 初始化事件总线
  if (config?.eventBus) {
    initializeGlobalEventBus(config.eventBus.maxListeners)
  } else {
    initializeGlobalEventBus()
  }

  // 初始化日志管理器
  if (config?.logger) {
    const logManager = LogManager.getInstance()
    logManager.setGlobalConfig({
      level: config.logger.level as any || 'info',
      enableConsole: config.logger.enableConsole ?? true,
      enableFile: config.logger.enableFile ?? false
    })
  }

  console.log('PowerTools Utils initialized successfully')
}

/**
 * 清理工具类资源
 */
export function cleanupUtils(): void {
  // 清理事件总线
  destroyGlobalEventBus()

  // 清理日志管理器
  LogManager.getInstance().close()

  console.log('PowerTools Utils cleaned up successfully')
}

// ============= 默认导出 =============
export default {
  // 类
  IdGenerator,
  UUIDGenerator,
  TimestampIdGenerator,
  SessionIdGenerator,
  ChannelIdGenerator,
  EventBus,
  TypedEventBus,
  Encryption,
  PasswordEncryption,
  Logger,
  LogManager,
  WaitObject,
  TimeoutWaitObject,
  CancellableWaitObject,
  WaitObjectFactory,

  // 工具函数
  Utils,

  // 初始化函数
  initializeUtils,
  cleanupUtils,

  // 常量
  DEFAULT_CONFIG,
  ERROR_CODES
}
