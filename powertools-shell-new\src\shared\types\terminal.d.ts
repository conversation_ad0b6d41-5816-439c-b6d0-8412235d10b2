/**
 * 终端相关类型定义
 * 定义终端、XTerm、主题等相关类型
 */

// ============= XTerm 相关类型 =============

export interface TerminalOptions {
  cols?: number
  rows?: number
  cursorBlink?: boolean
  cursorStyle?: 'block' | 'underline' | 'bar'
  cursorWidth?: number
  bellStyle?: 'none' | 'visual' | 'sound' | 'both'
  fontSize?: number
  fontFamily?: string
  fontWeight?: 'normal' | 'bold' | '100' | '200' | '300' | '400' | '500' | '600' | '700' | '800' | '900'
  fontWeightBold?: 'normal' | 'bold' | '100' | '200' | '300' | '400' | '500' | '600' | '700' | '800' | '900'
  lineHeight?: number
  letterSpacing?: number
  scrollback?: number
  tabStopWidth?: number
  theme?: TerminalTheme
  allowTransparency?: boolean
  altClickMovesCursor?: boolean
  convertEol?: boolean
  disableStdin?: boolean
  macOptionIsMeta?: boolean
  macOptionClickForcesSelection?: boolean
  minimumContrastRatio?: number
  rightClickSelectsWord?: boolean
  screenReaderMode?: boolean
  smoothScrollDuration?: number
  windowsMode?: boolean
  wordSeparator?: string
}

export interface TerminalTheme {
  foreground?: string
  background?: string
  cursor?: string
  cursorAccent?: string
  selection?: string
  black?: string
  red?: string
  green?: string
  yellow?: string
  blue?: string
  magenta?: string
  cyan?: string
  white?: string
  brightBlack?: string
  brightRed?: string
  brightGreen?: string
  brightYellow?: string
  brightBlue?: string
  brightMagenta?: string
  brightCyan?: string
  brightWhite?: string
}

export interface TerminalAddon {
  name: string
  activate: (terminal: any) => void
  dispose: () => void
}

// ============= 终端实例接口 =============

export interface TerminalInstance {
  id: string
  sessionId: string
  element: HTMLElement
  terminal: any // XTerm Terminal instance
  addons: Map<string, TerminalAddon>
  
  // 基础方法
  write(data: string | Uint8Array): void
  writeln(data: string): void
  clear(): void
  reset(): void
  focus(): void
  blur(): void
  
  // 尺寸管理
  resize(cols: number, rows: number): void
  fit(): void
  
  // 选择和复制
  select(column: number, row: number, length: number): void
  selectAll(): void
  selectLines(start: number, end: number): void
  getSelection(): string
  clearSelection(): void
  
  // 搜索
  findNext(term: string, options?: SearchOptions): boolean
  findPrevious(term: string, options?: SearchOptions): boolean
  
  // 事件监听
  onData(listener: (data: string) => void): void
  onResize(listener: (size: { cols: number; rows: number }) => void): void
  onSelectionChange(listener: () => void): void
  onTitleChange(listener: (title: string) => void): void
  
  // 插件管理
  loadAddon(addon: TerminalAddon): void
  unloadAddon(name: string): void
  
  // 清理
  dispose(): void
}

export interface SearchOptions {
  regex?: boolean
  wholeWord?: boolean
  caseSensitive?: boolean
  incremental?: boolean
}

// ============= 终端配置接口 =============

export interface TerminalProfile {
  id: string
  name: string
  description?: string
  options: TerminalOptions
  keyBindings?: KeyBinding[]
  isDefault?: boolean
  createdAt: Date
  updatedAt: Date
}

export interface KeyBinding {
  key: string
  command: string
  when?: string
  args?: any
}

// ============= 终端主题接口 =============

export interface ThemeDefinition {
  id: string
  name: string
  description?: string
  author?: string
  version?: string
  type: 'light' | 'dark' | 'auto'
  terminal: TerminalTheme
  ui?: {
    background?: string
    foreground?: string
    border?: string
    accent?: string
    selection?: string
    hover?: string
    active?: string
    inactive?: string
  }
  syntax?: {
    keyword?: string
    string?: string
    comment?: string
    number?: string
    operator?: string
    function?: string
    variable?: string
    type?: string
  }
}

// ============= 字体管理接口 =============

export interface FontInfo {
  family: string
  style: string
  weight: string
  size: number
  lineHeight: number
  letterSpacing: number
}

export interface FontManager {
  getSystemFonts(): Promise<string[]>
  getMonospaceFonts(): Promise<string[]>
  validateFont(family: string): Promise<boolean>
  getFontInfo(family: string): Promise<FontInfo | null>
  preloadFont(family: string): Promise<void>
}

// ============= 终端管理器接口 =============

export interface TerminalManager {
  // 实例管理
  createTerminal(sessionId: string, options?: TerminalOptions): Promise<TerminalInstance>
  getTerminal(id: string): TerminalInstance | null
  getTerminalBySessionId(sessionId: string): TerminalInstance | null
  getTerminals(): TerminalInstance[]
  destroyTerminal(id: string): Promise<void>
  
  // 配置管理
  getProfiles(): TerminalProfile[]
  getProfile(id: string): TerminalProfile | null
  createProfile(profile: Omit<TerminalProfile, 'id' | 'createdAt' | 'updatedAt'>): Promise<TerminalProfile>
  updateProfile(id: string, updates: Partial<TerminalProfile>): Promise<void>
  deleteProfile(id: string): Promise<void>
  setDefaultProfile(id: string): Promise<void>
  
  // 主题管理
  getThemes(): ThemeDefinition[]
  getTheme(id: string): ThemeDefinition | null
  loadTheme(theme: ThemeDefinition): Promise<void>
  setTheme(id: string): Promise<void>
  createCustomTheme(theme: Omit<ThemeDefinition, 'id'>): Promise<ThemeDefinition>
  
  // 字体管理
  getFontManager(): FontManager
  
  // 全局设置
  setGlobalOptions(options: Partial<TerminalOptions>): Promise<void>
  getGlobalOptions(): TerminalOptions
  
  // 事件
  on(event: string, listener: (...args: any[]) => void): void
  off(event: string, listener: (...args: any[]) => void): void
}

// ============= 终端事件类型 =============

export interface TerminalEvent {
  type: 'created' | 'destroyed' | 'data' | 'resize' | 'title' | 'selection'
  terminalId: string
  timestamp: Date
  data?: any
}

export interface TerminalDataEvent extends TerminalEvent {
  type: 'data'
  data: {
    content: string
    source: 'input' | 'output'
  }
}

export interface TerminalResizeEvent extends TerminalEvent {
  type: 'resize'
  data: {
    cols: number
    rows: number
  }
}

export interface TerminalTitleEvent extends TerminalEvent {
  type: 'title'
  data: {
    title: string
  }
}

export interface TerminalSelectionEvent extends TerminalEvent {
  type: 'selection'
  data: {
    text: string
    hasSelection: boolean
  }
}

// ============= 内置主题定义 =============

export const BUILTIN_THEMES: Record<string, ThemeDefinition> = {
  default: {
    id: 'default',
    name: 'Default',
    type: 'light',
    terminal: {
      foreground: '#000000',
      background: '#ffffff',
      cursor: '#000000',
      selection: '#b5d5ff'
    }
  },
  dark: {
    id: 'dark',
    name: 'Dark',
    type: 'dark',
    terminal: {
      foreground: '#ffffff',
      background: '#000000',
      cursor: '#ffffff',
      selection: '#404040'
    }
  }
}

// ============= 导出类型 =============

export type {
  TerminalOptions,
  TerminalTheme,
  TerminalAddon,
  TerminalInstance,
  SearchOptions,
  TerminalProfile,
  KeyBinding,
  ThemeDefinition,
  FontInfo,
  FontManager,
  TerminalManager,
  TerminalEvent,
  TerminalDataEvent,
  TerminalResizeEvent,
  TerminalTitleEvent,
  TerminalSelectionEvent
}
