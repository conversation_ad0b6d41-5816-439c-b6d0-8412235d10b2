<template>
  <div id="app" class="main-window">
    <NxLayout>
      <template #main-panel>
        <router-view />
      </template>
    </NxLayout>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { storeToRefs } from 'pinia'
import NxLayout from '@/layout/NxLayout.vue'
import { useSettingStore } from '@/stores'

const settingStore = useSettingStore()
const { theme } = storeToRefs(settingStore)
const router = useRouter()

onMounted(async () => {
  try {
    // 初始化主题
    await settingStore.changeTheme(theme.value)

    // 开发环境不自动跳转到首页
    if (import.meta.env.PROD) {
      router.push({ name: 'Home' })
    }

    console.log('PowerTools Shell App mounted successfully')

  } catch (error) {
    console.error('Failed to initialize app:', error)
  }
})
</script>
