{"name": "powertools-shell", "version": "2.0.0", "description": "PowerTools Shell - Modern Terminal and SSH Client", "main": "./out/main/index.js", "author": "NxShell Team", "homepage": "https://github.com/nxshell/shell", "scripts": {"format": "prettier --write .", "lint": "eslint --cache .", "typecheck:node": "tsc --noEmit -p tsconfig.node.json --composite false", "typecheck:web": "vue-tsc --noEmit -p tsconfig.web.json --composite false", "typecheck": "npm run typecheck:node && npm run typecheck:web", "start": "electron-vite preview", "dev": "electron-vite dev", "build": "npm run typecheck && electron-vite build", "postinstall": "electron-builder install-app-deps", "build:unpack": "npm run build && electron-builder --dir", "build:win": "npm run build && electron-builder --win", "build:mac": "npm run build && electron-builder --mac", "build:linux": "npm run build && electron-builder --linux"}, "dependencies": {"@electron-toolkit/preload": "^3.0.1", "@electron-toolkit/utils": "^4.0.0", "electron-updater": "^6.3.9", "@better-scroll/core": "^2.5.0", "@better-scroll/mouse-wheel": "^2.5.0", "@better-scroll/slide": "^2.5.1", "@codemirror/lang-cpp": "^6.0.0", "@codemirror/lang-css": "^6.0.0", "@codemirror/lang-java": "^6.0.0", "@codemirror/lang-javascript": "6.0.2", "@codemirror/lang-json": "^6.0.0", "@codemirror/lang-markdown": "^6.0.0", "@codemirror/lang-php": "^6.0.0", "@codemirror/lang-python": "^6.0.0", "@codemirror/lang-xml": "^6.0.0", "@fontsource/dejavu-mono": "^4.5.4", "axios": "^1.6.0", "cm6-theme-basic-dark": "^0.2.0", "cm6-theme-basic-light": "^0.2.0", "cm6-theme-gruvbox-dark": "^0.2.0", "cm6-theme-gruvbox-light": "^0.2.0", "cm6-theme-nord": "^0.2.0", "cm6-theme-solarized-dark": "^0.2.0", "cm6-theme-solarized-light": "^0.2.0", "codemirror": "6.0.1", "element-plus": "^2.8.0", "element-resize-detector": "^1.2.1", "font-list": "^1.3.1", "iconv-lite": "^0.6.0", "lodash": "^4.17.21", "mousetrap": "1.6.5", "node-pty": "^1.0.0", "nxshell-ftp": "^0.3.11", "nxshell-socksv5": "^0.0.6", "nxshell-ssh2": "^1.6.1", "nxshell-vnc": "^1.3.0", "nxshell-zmodem.js": "^0.1.10", "pinia": "^2.2.0", "semver": "^7.6.0", "serialport": "^12.0.0", "telnet-client": "^1.4.9", "uuid": "^10.0.0", "vscode-material-icon-theme-js": "^1.0.7", "vue-i18n": "^10.0.0", "vue-router": "^4.4.0", "webdav": "^5.7.0", "winston": "^3.14.0", "xterm": "5.3.0", "xterm-addon-fit": "0.8.0", "xterm-addon-search": "0.13.0", "xterm-addon-web-links": "0.9.0", "xterm-addon-webgl": "0.16.0", "xterm-theme": "^1.1.0"}, "devDependencies": {"@electron-toolkit/eslint-config-prettier": "3.0.0", "@electron-toolkit/eslint-config-ts": "^3.0.0", "@electron-toolkit/tsconfig": "^1.0.1", "@types/lodash": "^4.17.0", "@types/mousetrap": "^1.6.15", "@types/node": "^22.14.1", "@types/semver": "^7.5.0", "@types/uuid": "^10.0.0", "@vitejs/plugin-vue": "^5.2.3", "electron": "^35.1.5", "electron-builder": "^25.1.8", "electron-rebuild": "^3.6.0", "electron-vite": "^3.1.0", "eslint": "^9.24.0", "eslint-plugin-vue": "^10.0.0", "prettier": "^3.5.3", "sass": "^1.80.0", "sass-loader": "^16.0.0", "svg-sprite-loader": "^6.0.11", "svgo": "^3.3.0", "typescript": "^5.8.3", "unplugin-auto-import": "^0.18.0", "unplugin-vue-components": "^0.27.0", "vite": "^6.2.6", "vue": "^3.5.13", "vue-eslint-parser": "^10.1.3", "vue-tsc": "^2.2.8"}, "prettier": {"printWidth": 170, "tabWidth": 4, "useTabs": true, "semi": false, "singleQuote": false, "proseWrap": "never", "bracketSpacing": true, "bracketSameLine": false, "endOfLine": "lf", "trailingComma": "none", "vueIndentScriptAndStyle": false}}