{"name": "powertools-shell", "version": "2.0.0", "description": "PowerTools Shell - Modern Terminal and SSH Client", "main": "./out/main/index.js", "author": "NxShell Team", "homepage": "https://github.com/nxshell/shell", "packageManager": "pnpm@8.15.0", "scripts": {"format": "prettier --write .", "lint": "eslint --cache .", "typecheck:node": "tsc --noEmit -p tsconfig.node.json --composite false", "typecheck:web": "vue-tsc --noEmit -p tsconfig.web.json --composite false", "typecheck": "pnpm run typecheck:node && pnpm run typecheck:web", "start": "electron-vite preview", "dev": "electron-vite dev", "build": "pnpm run typecheck && electron-vite build", "postinstall": "electron-builder install-app-deps", "build:unpack": "pnpm run build && electron-builder --dir", "build:win": "pnpm run build && electron-builder --win", "build:mac": "pnpm run build && electron-builder --mac", "build:linux": "pnpm run build && electron-builder --linux"}, "dependencies": {"@electron-toolkit/preload": "^3.0.1", "@electron-toolkit/utils": "^4.0.0", "electron-updater": "^6.3.9", "@fontsource/dejavu-mono": "^4.5.4", "element-plus": "^2.8.0", "pinia": "^2.2.0", "vue": "^3.5.13", "vue-i18n": "^10.0.0", "vue-router": "^4.4.0"}, "devDependencies": {"@electron-toolkit/eslint-config-prettier": "3.0.0", "@electron-toolkit/eslint-config-ts": "^3.0.0", "@electron-toolkit/tsconfig": "^1.0.1", "@types/node": "^22.14.1", "@vitejs/plugin-vue": "^5.2.3", "electron": "^35.1.5", "electron-builder": "^25.1.8", "electron-vite": "^3.1.0", "eslint": "^9.24.0", "eslint-plugin-vue": "^10.0.0", "prettier": "^3.5.3", "sass": "^1.80.0", "typescript": "^5.8.3", "unplugin-auto-import": "^0.18.0", "unplugin-vue-components": "^0.27.0", "vite": "^6.2.6", "vue-tsc": "^2.2.8"}}