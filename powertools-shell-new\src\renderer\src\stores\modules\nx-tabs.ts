/**
 * 标签页状态管理
 * 迁移自原有的 nx-tabs store
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export interface INxTabProps {
  id: string
  name: string
  type: string
  sessionId?: string
  icon?: string
  closable?: boolean
  active?: boolean
  data?: any
}

export const useNxTabsStore = defineStore('nxTabs', () => {
  // 状态
  const tabData = ref<INxTabProps[]>([])
  const currentActive = ref<number>(0)
  const checkedTabType = ref<string>('')
  const showTabs = ref<boolean>(true)
  const configPanel = ref<boolean>(true)
  const noConfirm = ref<boolean>(false)
  const editorChange = ref<boolean>(false)

  // 标签图标映射
  const tabIcon: Record<string, string> = {
    login: 'user',
    welcome: 'logo',
    sftp: 'folder-sftp-open',
    globalsetting: 'n-setting',
    shell: 'terminal',
    editor: 'edit',
    vnc: 'desktop',
    ftp: 'folder',
    webdav: 'cloud'
  }

  // 计算属性
  const activeTab = computed(() => {
    return tabData.value[currentActive.value]
  })

  const tabCount = computed(() => {
    return tabData.value.length
  })

  const hasActiveTabs = computed(() => {
    return tabData.value.length > 0
  })

  const closableTabs = computed(() => {
    return tabData.value.filter(tab => tab.closable !== false)
  })

  // 动作
  const addTab = (tab: Omit<INxTabProps, 'id'>) => {
    const newTab: INxTabProps = {
      id: `tab-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      icon: tabIcon[tab.type] || 'file',
      closable: true,
      active: false,
      ...tab
    }

    // 检查是否已存在相同的标签
    const existingIndex = tabData.value.findIndex(
      t => t.type === tab.type && t.sessionId === tab.sessionId
    )

    if (existingIndex !== -1) {
      // 如果已存在，激活该标签
      setActiveTab(existingIndex)
      return tabData.value[existingIndex]
    }

    // 添加新标签
    tabData.value.push(newTab)
    setActiveTab(tabData.value.length - 1)

    console.log(`Added tab: ${newTab.name} (${newTab.type})`)
    return newTab
  }

  const removeTab = (index: number) => {
    if (index < 0 || index >= tabData.value.length) {
      return
    }

    const tab = tabData.value[index]
    tabData.value.splice(index, 1)

    // 调整当前激活的标签索引
    if (currentActive.value >= tabData.value.length) {
      currentActive.value = Math.max(0, tabData.value.length - 1)
    } else if (currentActive.value > index) {
      currentActive.value--
    }

    console.log(`Removed tab: ${tab.name} (${tab.type})`)
  }

  const removeTabById = (id: string) => {
    const index = tabData.value.findIndex(tab => tab.id === id)
    if (index !== -1) {
      removeTab(index)
    }
  }

  const setActiveTab = (index: number) => {
    if (index >= 0 && index < tabData.value.length) {
      // 更新所有标签的激活状态
      tabData.value.forEach((tab, i) => {
        tab.active = i === index
      })
      
      currentActive.value = index
      console.log(`Activated tab: ${tabData.value[index].name}`)
    }
  }

  const setActiveTabById = (id: string) => {
    const index = tabData.value.findIndex(tab => tab.id === id)
    if (index !== -1) {
      setActiveTab(index)
    }
  }

  const updateTab = (index: number, updates: Partial<INxTabProps>) => {
    if (index >= 0 && index < tabData.value.length) {
      Object.assign(tabData.value[index], updates)
      console.log(`Updated tab: ${tabData.value[index].name}`)
    }
  }

  const updateTabById = (id: string, updates: Partial<INxTabProps>) => {
    const index = tabData.value.findIndex(tab => tab.id === id)
    if (index !== -1) {
      updateTab(index, updates)
    }
  }

  const findTabBySessionId = (sessionId: string): INxTabProps | undefined => {
    return tabData.value.find(tab => tab.sessionId === sessionId)
  }

  const findTabsByType = (type: string): INxTabProps[] => {
    return tabData.value.filter(tab => tab.type === type)
  }

  const closeAllTabs = () => {
    const closableCount = closableTabs.value.length
    tabData.value = tabData.value.filter(tab => tab.closable === false)
    currentActive.value = 0
    
    console.log(`Closed ${closableCount} tabs`)
  }

  const closeOtherTabs = (keepIndex: number) => {
    const keepTab = tabData.value[keepIndex]
    if (!keepTab) return

    const newTabs = tabData.value.filter((tab, index) => 
      index === keepIndex || tab.closable === false
    )
    
    tabData.value = newTabs
    currentActive.value = newTabs.findIndex(tab => tab.id === keepTab.id)
    
    console.log(`Closed other tabs, kept: ${keepTab.name}`)
  }

  const toggleConfigPanel = () => {
    configPanel.value = !configPanel.value
    console.log(`Config panel ${configPanel.value ? 'opened' : 'closed'}`)
  }

  const toggleShowTabs = () => {
    showTabs.value = !showTabs.value
    console.log(`Tabs ${showTabs.value ? 'shown' : 'hidden'}`)
  }

  const setCheckedTabType = (type: string) => {
    checkedTabType.value = type
  }

  const setNoConfirm = (value: boolean) => {
    noConfirm.value = value
  }

  const setEditorChange = (value: boolean) => {
    editorChange.value = value
  }

  const clearAllTabs = () => {
    tabData.value = []
    currentActive.value = 0
    console.log('All tabs cleared')
  }

  return {
    // 状态
    tabData,
    currentActive,
    checkedTabType,
    showTabs,
    configPanel,
    noConfirm,
    editorChange,
    
    // 计算属性
    activeTab,
    tabCount,
    hasActiveTabs,
    closableTabs,
    
    // 动作
    addTab,
    removeTab,
    removeTabById,
    setActiveTab,
    setActiveTabById,
    updateTab,
    updateTabById,
    findTabBySessionId,
    findTabsByType,
    closeAllTabs,
    closeOtherTabs,
    toggleConfigPanel,
    toggleShowTabs,
    setCheckedTabType,
    setNoConfirm,
    setEditorChange,
    clearAllTabs
  }
})

export default useNxTabsStore
