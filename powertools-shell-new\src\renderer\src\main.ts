import './assets/main.css'
import './assets/scss/default.scss'
import '@fontsource/dejavu-mono'

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { createI18n } from 'vue-i18n'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'

import App from './App.vue'
import router from './router'
import { setupComponents } from './components'
import { setupServices } from './services'
import { setupIcons } from './icons'

// 创建应用实例
const app = createApp(App)

// 创建Pinia状态管理
const pinia = createPinia()

// 创建国际化
const i18n = createI18n({
  legacy: false,
  locale: 'zh-CN',
  fallbackLocale: 'en-US',
  allowComposition: true,
  silentTranslationWarn: true,
  missingWarn: false,
  fallbackWarn: false,
  messages: {
    'zh-CN': {},
    'en-US': {}
  }
})

// 安装插件
app.use(pinia)
app.use(router)
app.use(i18n)
app.use(ElementPlus)

// 设置组件
setupComponents(app)

// 设置服务
setupServices(app)

// 设置图标
setupIcons(app)

// 初始化服务并挂载应用
async function initializeApp() {
  try {
    // 等待PowerTools API就绪
    if (typeof window.powertools === 'undefined') {
      console.warn('PowerTools API not available, running in development mode')
    }

    // 初始化服务
    // await initServices()

    // 挂载应用
    app.mount('#app')

    console.log('PowerTools Shell Vue 3 app initialized successfully')

  } catch (error) {
    console.error('Failed to initialize app:', error)
  }
}

// 禁用刷新快捷键
window.addEventListener('keydown', (evt) => {
  if (evt.ctrlKey && evt.key === 'r') {
    evt.preventDefault()
  }
  if (evt.metaKey && evt.key === 'r') {
    evt.preventDefault()
  }
})

// 启动应用
initializeApp()
