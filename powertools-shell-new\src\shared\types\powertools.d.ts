/**
 * PowerTools Shell 核心类型定义
 * 基于原有架构重新设计的类型安全接口
 */

// ============= 基础类型 =============

export interface PowerToolsWindow extends Window {
  powertools: PowerToolsAPI
  electron: any
}

// ============= 会话相关类型 =============

export type SessionType = 'ssh' | 'sftp' | 'ftp' | 'telnet' | 'localshell' | 'serialport' | 'vnc' | 'webdav'

export interface SessionConfig {
  uuid: string
  name: string
  type: 'folder' | 'node'
  sessType?: SessionType
  description?: string
  config?: Record<string, any>
  subSessions?: SessionConfig[]
}

export interface SessionInstance {
  id: string
  name: string
  type: SessionType
  uuid: string
  config: Record<string, any>
}

// ============= 通信相关类型 =============

export interface RPCRequest {
  id: string
  method: string
  params: any[]
}

export interface RPCResponse {
  id: string
  result?: any
  error?: {
    code: number
    message: string
  }
}

export interface ChannelData {
  channelId: string
  data: any
  type?: string
}

export interface HsIPCChannel {
  channelId: string
  send(data: any): Promise<void>
  on(event: 'data', callback: (data: any) => void): void
  close(): void
}

// ============= 服务接口类型 =============

export interface PowerToolsService {
  createChannel(): any
  createNodeSessionInstance(uuid: string, config: SessionConfig): Promise<number>
  getNodeSessionInstanceByUUID(uuid: string): Promise<any>
  createDataTransfer(): any
  createFileStorage(): any
  getSerialPorts(): Promise<Array<{ path: string }>>
  createLogger(): any
  getSystemFonts(): Promise<string[]>
  getHsIPCHandle(): Promise<string>
}

// ============= PowerTools API 主接口 =============

export interface PowerToolsAPI {
  // 服务管理
  getService(serviceName?: string): PowerToolsService
  
  // 窗口管理
  getCurrentWindow(): any
  
  // 剪贴板操作
  clipboardReadText(): string
  clipboardWriteText(text: string): void
  
  // 外部链接
  openExterUrl(url: string): Promise<void>
  openDialog(url: string, options?: Record<string, any>): Window | null
  
  // 路径操作
  getAppDataDirty(): string
  getAppHomeDirty(): string
  getLogDirty(): string
  getAppPath(): string
  openPath(path: string): Promise<string>
  showItemInFolder(path: string): void
  
  // 应用信息
  getVersion(): string
  getPortable(): boolean
  getWebLink(): string
  getostype(): string
  
  // 高速IPC
  createHsIPC(unixFile: string): Promise<HsIPCChannel>
  
  // 屏幕录制
  captureStart(): Promise<void>
  captureStop(): Promise<Buffer | null>
}

// ============= 节点协议相关类型 =============

export interface NodeProtocol {
  type: SessionType
  name: string
  defaultPort?: number
  capabilities: string[]
}

export interface NodeConnection {
  id: string
  protocol: SessionType
  config: Record<string, any>
  status: 'connecting' | 'connected' | 'disconnected' | 'error'
}

export interface TerminalInstance {
  connId: string
  init(config: any): Promise<void>
  bindDataChannel(channelId: string): Promise<void>
  sendData(data: any): Promise<void>
  setWindowSize(cols: number, rows: number): Promise<void>
  openTunnel(): Promise<any>
  getConnId(): Promise<string>
  close(): Promise<void>
}

// ============= 文件系统相关类型 =============

export interface FileSystemEntry {
  name: string
  path: string
  type: 'file' | 'directory'
  size?: number
  modified?: Date
  permissions?: string
}

export interface FileSystemInstance {
  list(path: string): Promise<FileSystemEntry[]>
  read(path: string): Promise<Buffer>
  write(path: string, data: Buffer): Promise<void>
  delete(path: string): Promise<void>
  mkdir(path: string): Promise<void>
  stat(path: string): Promise<FileSystemEntry>
}

// ============= 存储相关类型 =============

export interface StorageInstance {
  read(key: string): Promise<any>
  save(key: string, data: any, encrypt?: boolean): Promise<void>
  delete(key: string): Promise<void>
  export(key: string, path: string): Promise<void>
  import(path: string, key: string): Promise<void>
  setConfigPath(path: string): Promise<void>
}

// ============= 全局声明 =============

declare global {
  interface Window {
    powertools: PowerToolsAPI
  }
  
  const powertools: PowerToolsAPI
}

export {}
