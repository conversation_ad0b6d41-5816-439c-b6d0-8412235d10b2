/**
 * 应用设置状态管理
 * 迁移自原有的 app-setting store
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { getProfile, updateProfile } from '@/services/globalSetting'

export type ThemeType = 'light' | 'dark' | 'pink'
export type LayoutModeType = 'normal' | 'row' | 'col' | 'grid'

export interface ISetting {
  theme: ThemeType
  userLock: boolean
  configPanel: boolean
  layoutMode: LayoutModeType
}

export const useSettingStore = defineStore('setting', () => {
  // 状态
  const theme = ref<ThemeType>('light')
  const userLock = ref<boolean>(false)
  const configPanel = ref<boolean>(true)
  const layoutMode = ref<LayoutModeType>('normal')

  // 计算属性
  const isDarkTheme = computed(() => theme.value === 'dark')
  const isPinkTheme = computed(() => theme.value === 'pink')
  const isLightTheme = computed(() => theme.value === 'light')

  // 动作
  const initializeSettings = async () => {
    try {
      const profile = await getProfile('xterm')
      if (profile?.theme) {
        theme.value = profile.theme as ThemeType
      }
      
      // 应用主题到DOM
      applyThemeToDOM(theme.value)
      
    } catch (error) {
      console.warn('Failed to load theme settings:', error)
      theme.value = 'light'
    }
  }

  const changeTheme = async (newTheme: ThemeType) => {
    try {
      const defaultSettings = await getProfile('xterm')
      await updateProfile('xterm', { 
        ...defaultSettings, 
        theme: newTheme 
      })
      
      theme.value = newTheme
      applyThemeToDOM(newTheme)
      
      console.log(`Theme changed to: ${newTheme}`)
      
    } catch (error) {
      console.error('Failed to change theme:', error)
      throw error
    }
  }

  const updateLayoutMode = (layout: LayoutModeType) => {
    layoutMode.value = layout
    console.log(`Layout mode changed to: ${layout}`)
  }

  const toggleUserLock = () => {
    userLock.value = !userLock.value
    console.log(`User lock ${userLock.value ? 'enabled' : 'disabled'}`)
  }

  const toggleConfigPanel = () => {
    configPanel.value = !configPanel.value
    console.log(`Config panel ${configPanel.value ? 'opened' : 'closed'}`)
  }

  // 辅助函数
  const applyThemeToDOM = (themeValue: ThemeType) => {
    if (typeof document !== 'undefined') {
      document.documentElement.setAttribute('nx-theme', themeValue)
      
      // 添加主题类名到body
      document.body.classList.remove('theme-light', 'theme-dark', 'theme-pink')
      document.body.classList.add(`theme-${themeValue}`)
    }
  }

  const resetSettings = () => {
    theme.value = 'light'
    userLock.value = false
    configPanel.value = true
    layoutMode.value = 'normal'
    
    applyThemeToDOM('light')
    console.log('Settings reset to defaults')
  }

  const exportSettings = (): ISetting => {
    return {
      theme: theme.value,
      userLock: userLock.value,
      configPanel: configPanel.value,
      layoutMode: layoutMode.value
    }
  }

  const importSettings = (settings: Partial<ISetting>) => {
    if (settings.theme) {
      theme.value = settings.theme
      applyThemeToDOM(settings.theme)
    }
    if (typeof settings.userLock === 'boolean') {
      userLock.value = settings.userLock
    }
    if (typeof settings.configPanel === 'boolean') {
      configPanel.value = settings.configPanel
    }
    if (settings.layoutMode) {
      layoutMode.value = settings.layoutMode
    }
    
    console.log('Settings imported:', settings)
  }

  // 初始化设置
  initializeSettings()

  return {
    // 状态
    theme,
    userLock,
    configPanel,
    layoutMode,
    
    // 计算属性
    isDarkTheme,
    isPinkTheme,
    isLightTheme,
    
    // 动作
    initializeSettings,
    changeTheme,
    updateLayoutMode,
    toggleUserLock,
    toggleConfigPanel,
    resetSettings,
    exportSettings,
    importSettings
  }
})

export default useSettingStore
