/**
 * PowerTools Shell 类型定义统一导出
 * 提供所有类型的统一入口
 */

// ============= 核心类型导出 =============

export * from './powertools'
export * from './session'
export * from './terminal'
export * from './filesystem'
export * from './app'

// ============= 常用类型重新导出 =============

export type {
  // PowerTools 核心
  PowerToolsAPI,
  PowerToolsService,
  PowerToolsWindow,
  ElectronAPI,
  
  // 会话管理
  SessionType,
  SessionStatus,
  SessionConfig,
  SessionInstance,
  SessionManager,
  
  // 终端
  TerminalOptions,
  TerminalTheme,
  TerminalInstance,
  TerminalManager,
  
  // 文件系统
  FileEntry,
  FileSystemService,
  FileManager,
  TransferManager,
  
  // 应用
  AppConfig,
  AppInfo,
  Plugin,
  PluginManager,
  Logger,
  ApplicationManager
} from './powertools'

// ============= 工具类型定义 =============

/**
 * 深度可选类型
 */
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P]
}

/**
 * 深度只读类型
 */
export type DeepReadonly<T> = {
  readonly [P in keyof T]: T[P] extends object ? DeepReadonly<T[P]> : T[P]
}

/**
 * 可空类型
 */
export type Nullable<T> = T | null

/**
 * 可选类型
 */
export type Optional<T> = T | undefined

/**
 * 键值对类型
 */
export type KeyValuePair<K = string, V = any> = {
  key: K
  value: V
}

/**
 * 事件监听器类型
 */
export type EventListener<T = any> = (data: T) => void

/**
 * 异步函数类型
 */
export type AsyncFunction<T = any, R = any> = (data: T) => Promise<R>

/**
 * 回调函数类型
 */
export type Callback<T = any, R = void> = (data: T) => R

/**
 * 构造函数类型
 */
export type Constructor<T = {}> = new (...args: any[]) => T

/**
 * 类类型
 */
export type Class<T = {}> = Constructor<T> & { prototype: T }

/**
 * 函数参数类型
 */
export type Parameters<T extends (...args: any) => any> = T extends (...args: infer P) => any ? P : never

/**
 * 函数返回值类型
 */
export type ReturnType<T extends (...args: any) => any> = T extends (...args: any) => infer R ? R : any

/**
 * Promise 解析类型
 */
export type Awaited<T> = T extends Promise<infer U> ? U : T

/**
 * 数组元素类型
 */
export type ArrayElement<T> = T extends (infer U)[] ? U : never

/**
 * 对象值类型
 */
export type ValueOf<T> = T[keyof T]

/**
 * 排除类型
 */
export type Exclude<T, U> = T extends U ? never : T

/**
 * 提取类型
 */
export type Extract<T, U> = T extends U ? T : never

/**
 * 非空类型
 */
export type NonNullable<T> = T extends null | undefined ? never : T

/**
 * 必需类型
 */
export type Required<T> = {
  [P in keyof T]-?: T[P]
}

/**
 * 部分类型
 */
export type Partial<T> = {
  [P in keyof T]?: T[P]
}

/**
 * 只读类型
 */
export type Readonly<T> = {
  readonly [P in keyof T]: T[P]
}

/**
 * 记录类型
 */
export type Record<K extends keyof any, T> = {
  [P in K]: T
}

/**
 * 选择类型
 */
export type Pick<T, K extends keyof T> = {
  [P in K]: T[P]
}

/**
 * 忽略类型
 */
export type Omit<T, K extends keyof any> = Pick<T, Exclude<keyof T, K>>

// ============= 常量类型定义 =============

/**
 * 支持的会话类型
 */
export const SESSION_TYPES = [
  'ssh',
  'sftp', 
  'ftp',
  'telnet',
  'localshell',
  'serialport',
  'vnc',
  'webdav',
  'rdp',
  'mosh'
] as const

/**
 * 支持的主题类型
 */
export const THEME_TYPES = ['light', 'dark', 'auto'] as const

/**
 * 支持的日志级别
 */
export const LOG_LEVELS = ['debug', 'info', 'warn', 'error', 'fatal'] as const

/**
 * 支持的文件类型
 */
export const FILE_TYPES = ['file', 'directory', 'symlink', 'block', 'character', 'fifo', 'socket'] as const

/**
 * 支持的认证类型
 */
export const AUTH_TYPES = ['password', 'publickey', 'keyboard-interactive', 'none'] as const

// ============= 枚举类型定义 =============

/**
 * 会话状态枚举
 */
export enum SessionStatusEnum {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  RECONNECTING = 'reconnecting',
  ERROR = 'error',
  TIMEOUT = 'timeout'
}

/**
 * 文件操作类型枚举
 */
export enum FileOperationTypeEnum {
  COPY = 'copy',
  MOVE = 'move',
  DELETE = 'delete',
  RENAME = 'rename',
  CREATE = 'create',
  UPLOAD = 'upload',
  DOWNLOAD = 'download'
}

/**
 * 插件状态枚举
 */
export enum PluginStatusEnum {
  DISABLED = 'disabled',
  ENABLED = 'enabled',
  LOADING = 'loading',
  LOADED = 'loaded',
  ERROR = 'error'
}

/**
 * 更新状态枚举
 */
export enum UpdateStatusEnum {
  CHECKING = 'checking',
  AVAILABLE = 'available',
  DOWNLOADING = 'downloading',
  DOWNLOADED = 'downloaded',
  INSTALLING = 'installing',
  INSTALLED = 'installed',
  ERROR = 'error'
}

// ============= 错误类型定义 =============

/**
 * PowerTools 错误基类
 */
export class PowerToolsError extends Error {
  constructor(
    message: string,
    public code?: string,
    public category?: string,
    public data?: any
  ) {
    super(message)
    this.name = 'PowerToolsError'
  }
}

/**
 * 会话错误
 */
export class SessionError extends PowerToolsError {
  constructor(message: string, code?: string, data?: any) {
    super(message, code, 'session', data)
    this.name = 'SessionError'
  }
}

/**
 * 文件系统错误
 */
export class FileSystemError extends PowerToolsError {
  constructor(message: string, code?: string, data?: any) {
    super(message, code, 'filesystem', data)
    this.name = 'FileSystemError'
  }
}

/**
 * 插件错误
 */
export class PluginError extends PowerToolsError {
  constructor(message: string, code?: string, data?: any) {
    super(message, code, 'plugin', data)
    this.name = 'PluginError'
  }
}

/**
 * 网络错误
 */
export class NetworkError extends PowerToolsError {
  constructor(message: string, code?: string, data?: any) {
    super(message, code, 'network', data)
    this.name = 'NetworkError'
  }
}

// ============= 默认导出 =============

export default {
  SESSION_TYPES,
  THEME_TYPES,
  LOG_LEVELS,
  FILE_TYPES,
  AUTH_TYPES,
  SessionStatusEnum,
  FileOperationTypeEnum,
  PluginStatusEnum,
  UpdateStatusEnum,
  PowerToolsError,
  SessionError,
  FileSystemError,
  PluginError,
  NetworkError
}
