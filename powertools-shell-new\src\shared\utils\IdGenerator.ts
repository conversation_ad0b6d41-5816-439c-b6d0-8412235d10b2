/**
 * ID生成器
 * 重构自原有的 idGenerator.js，提供类型安全的ID生成功能
 */

/**
 * 整数ID生成器
 * ID最大有效个数为 Number.MAX_SAFE_INTEGER (9007199254740991)
 * 足够使用，所以不考虑回环的问题
 */
export class IdGenerator {
  private lastId: number

  constructor(initId: number = 0) {
    this.lastId = initId
  }

  /**
   * 获取下一个ID
   */
  getNext(): number {
    return ++this.lastId
  }

  /**
   * 获取当前ID（不递增）
   */
  getCurrent(): number {
    return this.lastId
  }

  /**
   * 重置ID生成器
   */
  reset(initId: number = 0): void {
    this.lastId = initId
  }

  /**
   * 设置当前ID
   */
  setCurrent(id: number): void {
    this.lastId = id
  }
}

/**
 * 全局ID生成器实例
 */
const globalIdGenerator = new IdGenerator()

/**
 * 获取全局唯一ID
 */
export function getGlobalId(): number {
  return globalIdGenerator.getNext()
}

/**
 * 重置全局ID生成器
 */
export function resetGlobalId(initId: number = 0): void {
  globalIdGenerator.reset(initId)
}

/**
 * 获取当前全局ID
 */
export function getCurrentGlobalId(): number {
  return globalIdGenerator.getCurrent()
}

/**
 * UUID生成器（基于crypto.randomUUID或fallback）
 */
export class UUIDGenerator {
  /**
   * 生成UUID v4
   */
  static generate(): string {
    if (typeof crypto !== 'undefined' && crypto.randomUUID) {
      return crypto.randomUUID()
    }
    
    // Fallback implementation
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
      const r = Math.random() * 16 | 0
      const v = c === 'x' ? r : (r & 0x3 | 0x8)
      return v.toString(16)
    })
  }

  /**
   * 生成短UUID（去掉连字符）
   */
  static generateShort(): string {
    return this.generate().replace(/-/g, '')
  }

  /**
   * 验证UUID格式
   */
  static validate(uuid: string): boolean {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
    return uuidRegex.test(uuid)
  }
}

/**
 * 时间戳ID生成器
 */
export class TimestampIdGenerator {
  private prefix: string
  private counter: number

  constructor(prefix: string = '') {
    this.prefix = prefix
    this.counter = 0
  }

  /**
   * 生成基于时间戳的ID
   */
  generate(): string {
    const timestamp = Date.now()
    const count = ++this.counter
    return `${this.prefix}${timestamp}-${count}`
  }

  /**
   * 生成带随机数的时间戳ID
   */
  generateWithRandom(): string {
    const timestamp = Date.now()
    const random = Math.random().toString(36).substr(2, 9)
    return `${this.prefix}${timestamp}-${random}`
  }

  /**
   * 重置计数器
   */
  resetCounter(): void {
    this.counter = 0
  }
}

/**
 * 会话ID生成器（专用于会话管理）
 */
export class SessionIdGenerator {
  private static instance: SessionIdGenerator
  private sessionCounter: number
  private instanceCounter: number

  private constructor() {
    this.sessionCounter = 0
    this.instanceCounter = 0
  }

  static getInstance(): SessionIdGenerator {
    if (!SessionIdGenerator.instance) {
      SessionIdGenerator.instance = new SessionIdGenerator()
    }
    return SessionIdGenerator.instance
  }

  /**
   * 生成会话配置ID
   */
  generateConfigId(): number {
    return ++this.sessionCounter
  }

  /**
   * 生成会话实例ID
   */
  generateInstanceId(): number {
    return ++this.instanceCounter
  }

  /**
   * 生成会话UUID
   */
  generateSessionUUID(): string {
    return UUIDGenerator.generate()
  }

  /**
   * 重置计数器
   */
  reset(): void {
    this.sessionCounter = 0
    this.instanceCounter = 0
  }
}

/**
 * 通道ID生成器（专用于IPC通道）
 */
export class ChannelIdGenerator {
  private static channelCounter = 0

  /**
   * 生成通道ID
   */
  static generate(prefix: string = 'channel'): string {
    const timestamp = Date.now()
    const counter = ++this.channelCounter
    const random = Math.random().toString(36).substr(2, 6)
    return `${prefix}-${timestamp}-${counter}-${random}`
  }

  /**
   * 生成高速IPC通道ID
   */
  static generateHsIPC(): string {
    return this.generate('hsipc')
  }

  /**
   * 生成RPC通道ID
   */
  static generateRPC(): string {
    return this.generate('rpc')
  }
}

// 导出默认实例
export default {
  IdGenerator,
  UUIDGenerator,
  TimestampIdGenerator,
  SessionIdGenerator,
  ChannelIdGenerator,
  getGlobalId,
  resetGlobalId,
  getCurrentGlobalId
}
